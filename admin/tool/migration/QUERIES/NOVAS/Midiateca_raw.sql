SELECT
    m.CODMIDIA,
    m.<PERSON><PERSON>,
    m.<PERSON><PERSON><PERSON><PERSON>,
    m.<PERSON><PERSON><PERSON><PERSON><PERSON>,
    m.DAT<PERSON><PERSON><PERSON><PERSON><PERSON>,
    m.ATIVO,
    m.CODTIPOMIDI<PERSON>,
    t.NOME                     AS TIPOMIDIA,
    m.CODRECURSOEXTERN<PERSON>,
    m.EXIBIREMMIDIASDIGITAIS,
    sc.CODCATEGORIA,
    sc.NO<PERSON>                    AS CATEGORIA,
    parent.CODCATEGORIA        AS CODCATEGORIA_PAI,
    parent.NOME                AS CATEGORIA_PAI_NOME
FROM dbo.LMSE_MDMIDIA            AS m
LEFT JOIN dbo.LMSE_MDTIPOMIDIA   AS t
    ON m.CODTIPOMIDIA      = t.CODTIPOMIDIA
LEFT JOIN dbo.LMSE_MDMIDIA_SISCATEGORIA AS mc
    ON m.CODMIDIA          = mc.CODMIDIA
LEFT JOIN dbo.LMSE_SISCATEGORIA AS sc
    ON mc.CODCATEGORIA      = sc.CODCATEGORIA
LEFT JOIN dbo.LMSE_SISCATEGORIA AS parent
    ON sc.CODC<PERSON><PERSON><PERSON>IAPAI   = parent.CODCATEGORIA;
