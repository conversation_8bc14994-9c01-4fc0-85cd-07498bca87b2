SELECT
    SAC.CODALUNO,
    U.NOME,
    U.EMAIL,
    U.CPF,
    SAC.CODCURSO,
    C.NOME AS CURSO,
    SAC.CODTURMA,
    T.NOMETURMA AS TURMA,
    SAC.Perc<PERSON>on<PERSON>,
    SAC.PercAproveitamento,
    SAC.FINALIZADO,
    SAC.DATAFIM AS DATACONCLUSAO,
    T.DATAINICIO AS INICIOTURMA,
    T.DATAFIM AS FIMTURMA
FROM SITUACAOALUNOCURSO AS SAC (NOLOCK)
INNER JOIN TURMA AS T (NOLOCK) ON (
    T.CODCURSO = SAC.CODCURSO
    AND T.CODTURMA = SAC.CODTURMA
)
INNER JOIN CURSOS AS C (NOLOCK) ON (C.CODCURSO = SAC.CODCURSO)
INNER JOIN USUARIOS AS U (NOLOCK) ON (SAC.CODALUNO = U.CODALUNO)
INNER JOIN LMS_CertificadosEmitidos AS CE (NOLOCK) ON (
    SAC.CODALUNO = CE.CODALUNO
    AND SAC.CODTURMA = CE.CODTURMA
)
WHERE T.TIPOTURMA = 'N'
  AND T.CompoeTrilhaDeAprendizagem = 0
  AND CE.CODTURMA IS NOT NULL;


SELECT
    U.CODALUNO, -- legacy_alunos.codaluno
    U.NOME, -- legacy_alunos.nome
    U.EMAIL, -- legacy_alunos.email
    U.CPF, -- legacy_alunos.cpf
    TA.Id AS CODTRILHA, -- legacy_trilhas.codtrilha
    TA.Name AS TRILHA, -- legacy_trilhas.nome
    TAC.Id AS CODTURMATRILHA, -- legacy_situacao_trilha.codigo_da_turma
    TAC.Name AS NOMETURMATRILHA, -- legacy_turmas_trilhas.nometurma
    TSC.TrainingState_PercentConcluded AS CONCLUSAO, -- legacy_situacao_trilha.porcent_conclusao
    TSC.TrainingState_PercentGrade AS PERCAPROVEITAMENTO, -- legacy_situacao_trilha.porcent_aproveitamento
    TSC.TrainingState_ConcludedOn AS DATACONCLUSAO, -- legacy_situacao_trilha.data_de_conclusao
    TAC.Period_From, -- legacy_situacao_trilha.data_de_inicio
    TAC.Period_To -- legacy_situacao_trilha.data_de_fim
FROM [dbo.LMS].TRN_StudentAndActivityCollectionClasses AS TSC (NOLOCK)
INNER JOIN [dbo.LMS].TRN_ActivityCollectionClasses AS TAC (NOLOCK) ON (
    TSC.ActivityCollectionId = TAC.ActivityCollectionId
    AND TSC.ActivityCollectionClassId = TAC.Id
)
INNER JOIN [dbo.LMS].TRN_Activities AS TA (NOLOCK) ON (
    TSC.ActivityCollectionId = TA.Id
)
INNER JOIN USUARIOS AS U (NOLOCK) ON (
    TSC.StudentId = U.TRAN_UserId
)
LEFT JOIN LMS_CertificadosEmitidos AS CE (NOLOCK) ON (
    CE.CODALUNO = U.CODALUNO
    AND CE.CODTURMATRILHA = TAC.Id
)
WHERE CE.CODTURMATRILHA IS NOT NULL;
