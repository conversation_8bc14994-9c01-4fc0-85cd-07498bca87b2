<?php namespace tool_migration\importers;

use cm_info;
use Exception;
use context_user;
use tool_lfxp\core\mod\cm_image;
use tool_migration\util\ingestion_helper;

class midia_importer {
    const EADTECH_AUDIO_FILE = 1;
    const EADTECH_DOCUMENT_FILE = 2;
    const EADTECH_IMAGE_FILE = 3;
    const EADTECH_LINK = 4;
    const EADTECH_VIDEO_FILE = 6;
    const EADTECH_EMBEDDED_VIDEO = 7;
    const EADTECH_OTHER = 8;
    const EADTECH_GOOGLE_DRIVE = 9;
    const EADTECH_DROPBOX = 10;
    const EADTECH_EVOLUTION_LIBRARY = 11;

    const AUDIO_FILE = 'audio';
    const VIDEO = 'video';
    const EMBEDDED_VIDEO = 'embedded_video';
    const URL = 'url';
    const IMAGE_FILE = 'image';
    const DOCX_FILE = 'docx';
    const PDF_FILE = 'pdf';
    const PPTX_FILE = 'pptx';
    const XLSX_FILE = 'xlsx';
    const ZIP_FILE = 'zip';
    const OTHER = 'other';

    const ALLOWED_TYPES = [
        self::AUDIO_FILE,
        self::EMBEDDED_VIDEO,
        self::IMAGE_FILE,
        self::DOCX_FILE,
        self::PDF_FILE,
        self::PPTX_FILE,
        self::XLSX_FILE,
    ];

    protected object $course;
    protected int $supervideo_module;
    protected int $resource_module;

    public function __construct(protected string $base_path, int $courseid) {
        global $CFG, $DB;
        require_once($CFG->dirroot.'/course/modlib.php');

        $this->course = get_course($courseid);
        $this->supervideo_module = $DB->get_field('modules', 'id', ['name' => 'supervideo']);
        $this->resource_module = $DB->get_field('modules', 'id', ['name' => 'resource']);
    }

    protected function get_original_thumbnail(array &$row) : ?string {
        if(empty($row['imagemexibicao'])){
            return null;
        }

        $filename = explode('?', $row['imagemexibicao']);
        $filename = reset($filename);
        $path = "$this->base_path/files/Thumbnails/$filename";
        if(file_exists($path)){
            return $path;
        }

        return null;
    }

    protected function eval_media_type(array &$row) : string {
        if (!empty($row['__mediatype'])) {
            return $row['__mediatype'];
        }

        $caminho = $row['caminho'] ?? '';
        $codtipomidia = (int) ($row['codtipomidia'] ?? 0);

        $type = match ($codtipomidia) {
            self::EADTECH_IMAGE_FILE        => self::IMAGE_FILE,
            self::EADTECH_AUDIO_FILE        => self::AUDIO_FILE,
            self::EADTECH_LINK              => self::URL,
            self::EADTECH_EMBEDDED_VIDEO    => self::EMBEDDED_VIDEO,
            default                         => null,
        };

        if (!empty($type)) {
            return $row['__mediatype'] = $type;
        }

        if ($codtipomidia === self::EADTECH_VIDEO_FILE) {
            $row['__mediatype'] = filter_var($caminho, FILTER_VALIDATE_URL)
                ? self::EMBEDDED_VIDEO
                : self::VIDEO;
            return $row['__mediatype'];
        }

        if ($codtipomidia === self::EADTECH_OTHER) {
            $ext = strtolower(pathinfo($caminho, PATHINFO_EXTENSION));
            $row['__mediatype'] = ($ext === 'zip') ? self::ZIP_FILE : self::OTHER;
            return $row['__mediatype'];
        }

        if ($codtipomidia === self::EADTECH_DOCUMENT_FILE) {
            $ext = strtolower(pathinfo($caminho, PATHINFO_EXTENSION));
            $row['__mediatype'] = match ($ext) {
                'docx' => self::DOCX_FILE,
                'pdf'  => self::PDF_FILE,
                'pptx' => self::PPTX_FILE,
                'xlsx' => self::XLSX_FILE,
                default => self::OTHER,
            };
            return $row['__mediatype'];
        }

        return $row['__mediatype'] = self::OTHER;
    }

    protected function get_default_thumbnail(array &$row) : ?string {
        $filename = match ($this->eval_media_type($row)) {
            self::EMBEDDED_VIDEO, self::VIDEO => 'video.png',
            self::AUDIO_FILE => 'audio.png',
            self::IMAGE_FILE => 'image.png',
            self::DOCX_FILE => 'docx.png',
            self::PDF_FILE => 'pdf.png',
            self::PPTX_FILE => 'pptx.png',
            self::XLSX_FILE => 'xlsx.png',
            self::URL => null,
            self::ZIP_FILE => null,
            self::OTHER => null,
            default => null, // Should no happen
        };

        if($filename){
            $path = "$this->base_path/default/$filename";
            if(file_exists($path)){
                return $path;
            }
        }

        return null;
    }

    protected function get_thumbnail(array &$row) : ?string {
        if($path = $this->get_original_thumbnail($row)){
            return $path;
        }

        return $this->get_default_thumbnail($row);
    }

    protected function get_file(array &$row) : ?string {
        $caminho = $row['caminho'];

        if(filter_var($caminho, FILTER_VALIDATE_URL)){
            return null;
        }

        $path = "$this->base_path/files/$caminho";
        if(file_exists($path)){
            return $path;
        }

        return null;
    }

    protected function get_url(array &$row) : ?string {
        $type = $this->eval_media_type($row);

        if($type == self::URL || $type == self::EMBEDDED_VIDEO){
            return filter_var($row['caminho'], FILTER_VALIDATE_URL) ? $row['caminho'] : null;
        }

        return null;
    }

    public function import_from_csv(array &$row) : void {
        $codmidia = $row['codmidia'];
        $type     = $this->eval_media_type($row);

        if(empty($row['codcategoria']) || strtolower($row['codcategoria']) == 'null'){
            $row['codcategoria'] = -1;
        }

        if(empty($row['categoria']) || strtolower($row['categoria']) == 'null'){
            $row['categoria'] = 'Sem categoria';
        }

        if (!in_array($type, self::ALLOWED_TYPES, true)) {
            throw new \DomainException("Mídia $codmidia: tipo '$type' não importável");
        }

        if ($this->get_course_module($codmidia)) {
            throw new \RuntimeException("Mídia $codmidia já foi importada");
        }

        if ($type === self::EMBEDDED_VIDEO) {
            $this->import_as_supervideo($row);
        } else {
            $this->import_as_resource($row);
        }
    }

    protected function import_as_supervideo(array &$row) : void {
        $codmidia  = $row['codmidia'];
        $section   = $this->get_or_create_section($row);
        $thumbnail = $this->get_thumbnail($row);

        $url = $this->get_url($row);
        if (!$url) {
            throw new \InvalidArgumentException("Mídia $codmidia: URL inválida");
        }

        $visibleoncoursepage = empty($row['exibiremmidiasdigitais']) ? 0 : 1;
        $active = empty($row['ativo']) ? 0 : 1;
        $visible = $visibleoncoursepage || $active;

        $data = [
            'name'                => $row['nome'],
            'modulename'          => 'supervideo',
            'module'              => $this->supervideo_module,
            'intro'               => $row['descricao'],
            'videourl'            => $url,
            'playersize'          => 1,
            'videofile'           => null,
            'showcontrols'        => 0,
            'autoplay'            => 0,
            'grade_approval'      => 0,
            'completionpercent'   => 0,
            'cmidnumber'          => $this->generate_module_idnumber($codmidia),
            'visible'             => (int) $visible,
            'visibleoncoursepage' => $visibleoncoursepage,
            'added'               => ingestion_helper::str_to_timestamp($row['datacriacao']),
        ];

        $this->create_course_module($section->section, $data, $thumbnail);
    }

    protected function import_as_resource(array &$row) : void {
        $codmidia  = $row['codmidia'];
        $section   = $this->get_or_create_section($row);
        $thumbnail = $this->get_thumbnail($row);

        $file = $this->get_file($row);
        if (!$file) {
            throw new \RuntimeException("Mídia $codmidia: arquivo não encontrado em {$row['caminho']}");
        }

        $visibleoncoursepage = empty($row['exibiremmidiasdigitais']) ? 0 : 1;
        $active = empty($row['ativo']) ? 0 : 1;
        $visible = $visibleoncoursepage || $active;

        $data = [
            'name'                => $row['nome'],
            'module'              => $this->resource_module,
            'intro'               => $row['descricao'],
            'modulename'          => 'resource',
            'cmidnumber'          => $this->generate_module_idnumber($codmidia),
            'visible'             => (int) $visible,
            'visibleoncoursepage' => $visibleoncoursepage,
            'added'               => ingestion_helper::str_to_timestamp($row['datacriacao']),
            'files'               => $this->create_draft_file_from_path($file)?->itemid,
            'tobemigrated'        => 0,
            'legacyfiles'         => 0,
            'legacyfileslast'     => 0,
            'display'             => 0,
            'displayoptions'      => null,
            'filterfiles'         => 0,
            'revision'            => 0,
        ];

        $this->create_course_module($section->section, $data, $thumbnail);
    }



    // SECTIONS LOGIC

    protected function create_section(int $codcategoria, string $categoria) : object {
        global $DB;
        try {
            $transaction = $DB->start_delegated_transaction();

            $section = course_create_section($this->course->id);
            $section->name = $categoria;
            $section->timemodified = time();
            $DB->update_record('course_sections', $section);

            $this->store_section_id($codcategoria, $section);
            
            $transaction->allow_commit();
            return $section;

        } catch (Exception $e) {
            $transaction->rollback($e);
        }
    }

    protected function get_section(int $codcategoria) : ?object {
        global $DB;

        $sectionid = get_config('tool_migration', $this->generate_section_idnumber($codcategoria));
        return $DB->get_record('course_sections', ['id'=> $sectionid]) ?: null;
    }

    protected function generate_section_idnumber(int $codcategoria) : string {
        return "eadt-md-cat-$codcategoria";
    }

    protected function store_section_id(int $codcategoria, object $section){
        $idnumber = $this->generate_section_idnumber($codcategoria);
        set_config($idnumber, $section->id, 'tool_migration');
    }

    protected function get_or_create_section(array &$row) : object {
        $codcategoria = $row['codcategoria'];
        $categoria = $row['categoria'];

        if(strtolower($codcategoria) == 'null' || empty($codcategoria)){
            throw new Exception($row['codmidia'] . " - Categoria nula");
        }

        if($section = $this->get_section($codcategoria)){
            return $section;
        }

        return $this->create_section($codcategoria, $categoria);
    }



    // MODULES LOGIC

    protected function generate_module_idnumber(int $codmidia) : string {
        return "eadt-md-$codmidia";
    }


    protected function define_module_defaults() : object {
        return (object) [       
            'section' => 0,
            'cmidnumber' => '',
            'idnumber' => null,
            'added' => 0,
            'score' => 0,
            'indent' => 0,
            'visible' => 1,
            'visibleoncoursepage' => 1,
            'visibleold' => 1,
            'groupmode' => 0,
            'groupingid' => 0,
            'completion' => COMPLETION_TRACKING_NONE,
            'completiongradeitemnumber' => null,
            'completionview' => COMPLETION_DISABLED,
            'completionexpected' => COMPLETION_DISABLED,
            'completionpassgrade' => COMPLETION_DISABLED,
            'showdescription' => 0,
            'availability' => null,
            'deletioninprogress' => 0,
            'downloadcontent' => DOWNLOAD_COURSE_CONTENT_ENABLED,
            'lang' => null,
            'tags' => [],

            // Generic module columns
            'name' => '',
            'intro' => '',
            'introformat' => FORMAT_MOODLE,

        ];
    }

    protected function create_course_module(int $section, array $data, ?string $image_path) : object {
        global $DB;
        try {
            $transaction = $DB->start_delegated_transaction();
            
            $module = $this->define_module_defaults();

            foreach ($data as $key => $value) {
                $module->$key = $value;
            }

            if(!empty($image_path)){
                $key = cm_image::FIELDNAME;
                $module->$key = $this->create_draft_file_from_path($image_path)?->itemid;
            }

            $module->section = $section;
            $module->course = $this->course->id;
            $moduleinfo = add_moduleinfo($module, $this->course);

            if(!empty($data['added'])){
                global $DB;
                $DB->update_record('course_modules', ['id' => $moduleinfo->id, 'added' => $data['added']]);
            }
           
            
            $transaction->allow_commit();
            return $moduleinfo;

        } catch (Exception $e) {
            /** @throws Exception */
            $transaction->rollback($e);
        }
    }

    protected function get_course_module(int $codmidia) : ?object {
        global $DB;
        $idnumber = $this->generate_module_idnumber($codmidia);

        return $DB->get_record('course_modules', ['idnumber' => $idnumber]) ?: null;
    }


    public static function create_draft_file_from_path(string $filepath) : object {
        global $USER;
        $draftid = file_get_unused_draft_itemid();
        $user_context = context_user::instance($USER->id);

        $file = (object) [
            'contextid' => $user_context->id,
            'component' => 'user',
            'filearea'  => 'draft',
            'itemid'    => $draftid,
            'filepath'  => '/',
            'filename'  => basename($filepath),
        ];

        $fs = get_file_storage();
        $fs->create_file_from_pathname($file, $filepath);
        return $file;
    }
}