<?php

require_once(__DIR__ . '/config.php');
require_once("$CFG->dirroot/lib/classes/cron.php");
require_once("$CFG->dirroot/course/lib.php");

use tool_migration\importers\midia_importer;

core\cron::setup_user();

$gallery_courseid = $DB->get_field('course', 'id', ['idnumber' => 'galeria-de-midias']);
$midiateca_path = $CFG->dataroot . '/eadtech/migration/midiateca';
$filepath = "$midiateca_path/data/midiateca-raw.csv";

$empty_course_first = false;
if($empty_course_first){
    $cms = $DB->get_records('course_modules', ['course' => $gallery_courseid]);
    foreach ($cms as $cm) {
        @course_delete_module($cm->id);
    }

    $format   = course_get_format($gallery_courseid);
    $sections = $DB->get_records('course_sections', ['course' => $gallery_courseid]);
    foreach ($sections as $section) {
        if ($section->section !== 0) {
            @$format->delete_section($section);
        }
    }

    rebuild_course_cache($gallery_courseid);
}

$reader = new tool_migration\importers\readers\csv_reader($filepath, $gallery_courseid);
$importer = new midia_importer($midiateca_path, $gallery_courseid);
$counter = 0;
$error_messages = [];

foreach ($reader->read('') as $row) {
    try {
        $importer->import_from_csv($row);
        $status = 'Importado';
        $counter++;
    } catch (\Throwable $th) {
        $status = 'Erro: ' . $th->getMessage();
        $error_messages[] = $status;
    }
    mtrace(sprintf('%s [%s]: %s', $row['codmidia'], $row['nome'], $status));
}

print("\nIMPORTADOS: $counter");
print("\n\nNÃO IMPORTADOS:");

foreach ($error_messages as $message) {
    print("\n$message");
}