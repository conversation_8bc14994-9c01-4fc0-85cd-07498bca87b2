# QUERIES A SEREM EXPORTADAS (EM ORDEM):
[x] Empresas.sql -> empresas.csv
[x] Estudantes.sql -> estudantes.csv
[x] Gestores.sql -> gestores.csv
[x] Cursos.sql -> cursos.csv
[x] Trilhas.sql -> trilhas.csv
[x] CursosExternos.sql -> cursos-externos.csv
[x] CorpoDocente.sql -> corpo-docente.csv
[x] Segmentos.sql -> segmentos.csv
[x] UsuariosPorSegmento.sql -> usuarios-segmentos.csv
[x] EmpresaAluno.sql -> empresa-aluno.csv
[x] TurmasDeCursos.sql -> turmas-cursos.csv
[x] TurmasDeTrilhas.sql -> turmas-trilhas.csv
[x] CorpoDocentePorTurma.sql -> corpo-docente-curso.csv
[x] CorpoDocentePorTurma.sql -> corpo-docente-trilha.csv
[x] PesquisasDeSatisfacaoV1.sql -> pesquisa-satisfacao-v1.csv
[x] TurmasPorSegmentos.sql -> segmentos-por-turma-curso.csv
[x] TurmasPorSegmentos.sql -> segmentos-por-turma-trilha.csv
[x] Trabalhos.sql -> trabalhos.csv
[x] TrabalhosAlunos.sql -> trabalhos-alunos.csv
[x] ProgressoDeAlunosEmCursos.sql -> progresso-de-alunos-em-cursos.csv
[x] ProgressoDeAlunosEmTrilhas.sql -> progress-de-alunos-em-trilhas.csv
[x] CertificadosTrilhas.sql -> certificados-trilhas.csv
[x] CertificadosDeCursos.sql -> certificados-cursos.csv
[x] PesquisasDeSatisfacaoV2.sql -> pesquisas-satisfacao-v2-cursos.csv
[x] PesquisasDeSatisfacaoV2.sql -> pesquisas-satisfacao-v2-trilhas.csv
[x] NOVAS/Topicos.sql -> topicos.csv
[x] NOVAS/HistoricoDetalhadoCurso.sql -> topicos-realizados.csv
[x] NOVAS/HistoricoDetalhadoCurso.sql -> notas-forums.csv
[x] NOVAS/ItensDeTrilha.sql -> itens-trilhas.csv
[x] NOVAS/NotasDeItemsDeTrilha.sql -> notas-itens-trilhas.csv
[x] NOVAS/IdsDegreed.sql -> degreed_content.csv

# IMPORTADOS PARA O MOODLE (EM ORDEM):
[x] Empresas
    [x] ingestion/ingest_empresas.php
    [x] import/import_empresas.php
[x] Estudantes
    [x] ingestion/ingest_estudantes.php
    [x] import/import_estudantes.php
[x] Gestores
    [x] ingestion/ingest_gestores.php
    [x] import/import_gestores.php
[x] Trilhas
    [x] ingestion/ingest_trilhas.php
    [x] import/import_trilhas.php
[x] Segmentos
    [x] ingestion/ingest_segmentos.php
    [x] import/import_segmentos.php
[x] Usuários x Segmentos
    [x] ingestion/ingest_usuarios_segmentos.php
    [x] import/import_usuarios_segmentos.php
[x] Cursos externos
    [x] ingestion/ingest_cursos_externos.php
    [x] import/import_cursos_externos.php  (Precisa dos arquivos de certificado na moodledata)
[x] Progresso em cursos
    [x] ingestion/ingest_progresso_em_cursos.php
    [x] import/import_conclusoes_de_cursos.php
[x] Progresso em trilhas
    [x] ingestion/ingest_progresso_em_trilhas.php
    [x] import/import_conclusoes_de_trilhas.php

# IMPORTADOS PARA O LOCAL_LEGACY (EM ORDEM)
[x] Empresas
    [x] legacy/ingest_empresas.php
[x] Alunos
    [x] legacy/ingest_alunos.php
[x] Gestores
    [x] legacy/ingest_gestores.php
[x] Cursos
    [x] legacy/ingest_cursos.php
[x] Trilhas
    [x] legacy/ingest_trilhas.php
[x] Progresso em cursos
    [x] legacy/ingest_situacao_cursos.php
[x] Progresso em trilhas
    [x] legacy/ingest_situacao_trilhas.php
[x] Corpo docente
    [x] legacy/ingest_corpo_docente.php
[x] Segmentos
    [x] legacy/ingest_segmentos.php
[x] Segmento x aluno
    [x] legacy/ingest_segmentos_alunos.php
[x] Empresa x aluno
    [x] legacy/ingest_empresa_alunos.php
[x] Turmas de cursos
    [x] legacy/ingest_turmas_cursos.php
[x] Turmas de trilhas
    [x] legacy/ingest_turmas_trilhas.php
[x] Corpo docente por turma de curso
    [x] legacy/ingest_corpo_docente_curso.php
[x] Corpo docente por turma de trilha
    [x] legacy/ingest_corpo_docente_trilha.php
[x] Trabalhos
    [x] legacy/ingest_trabalhos.php
[x] Trabalho x aluno
    [x] legacy/ingest_trabalhos_alunos.php
[x] Segmento x turma (curso)
    [x] legacy/ingest_segmentos_turma_curso.php
[x] Segmento x turma (trilha)
    [x] legacy/ingest_segmentos_turma_trilha.php
[x] Pesquisa de satisfação V1
    [x] legacy/ingest_feedback_v1.php
[x] Pesquisa de satisfação (Cursos)
    [x] legacy/ingest_feedback_cursos.php
[x] Pesquisa de satisfacao (Trilhas)
    [x] legacy/ingest_feedback_trilhas.php
[x] Importar certificados UCSEBRAE (legado²)
    [x] legacy/ingest_certificados_ucsebrae.php
[x] Tópicos de curso
    [x] legacy/ingest_topicos.php
[x] Historico de avaliações de curso
    [x] legacy/ingest_topicos_realizados.php
[x] Histórico de forums de curso
    [x] legacy/ingest_notas_forums.php
[x] Atividades de Trilha
    [x] legacy/ingest_itens_trilhas.php
[x] Histórico de atividades de trilha
    [x] legacy/ingest_itens_trilhas_alunos.php
[x] Importar ID de cursos na Degreed
    [x] legacy/ingest_degreed_course_config.php
[] Importar certificados de curso
    [] ...?
[] Importar certificados de trilhas
    [] ...?
[] Importar codigos de certificados da EADTECH
    [] ...? (cursos)
    [] ...? (trilhas)
[] Importar mídias para galeria de mídia
    [] ingest_midias.php



----

Certificado:

Se não tiver conteudo programático, não deve listar segunda página.
Vão adicionar os conteúdos programáticos depois.


