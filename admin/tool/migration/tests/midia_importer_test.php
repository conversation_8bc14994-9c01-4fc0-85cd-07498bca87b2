<?php

use tool_migration\importers\midia_importer;
use tool_lfxp\core\mod\cm_image;

class midia_importer_test extends advanced_testcase {
    /** @var string Base path pointing to fixtures directory */
    protected $basepath;

    /** @var stdClass The test course */
    protected $course;

    protected function setUp(): void {
        parent::setUp();

        // Point to existing fixtures under tests/fixtures/midiateca
        $this->basepath = __DIR__ . '/fixtures/midiateca';

        $this->resetAfterTest(true);
        $this->setAdminUser();
        $this->course = $this->getDataGenerator()->create_course();
    }

    /**
     * Invoke a protected method via reflection, passing arguments by reference when needed.
     *
     * @param object  $object  Instance whose method to invoke.
     * @param string  $method  Method name.
     * @param mixed[] $params  Parameters to pass (each will be passed by reference).
     * @return mixed
     */
    protected function invokeProtected($object, string $method, array $params = []) {
        $ref = new ReflectionClass($object);
        $m   = $ref->getMethod($method);
        $m->setAccessible(true);

        // Build arguments array with references
        $args = [];
        foreach ($params as $key => $value) {
            // assign to temp variable by reference
            $args[$key] = &$params[$key];
        }

        return $m->invokeArgs($object, $args);
    }

    /**
     *  @group !current
     */
    public function test_section_creation(): void {
        $importer = new midia_importer($this->basepath, $this->course->id);

        // Simulate a CSV row for category (needs to be passed by reference)
        $row = ['codcategoria' => 42, 'categoria' => 'Unit Test Category'];

        // First call: create section
        $section1 = $this->invokeProtected($importer, 'get_or_create_section', [&$row]);
        $this->assertNotEmpty($section1->id);
        $this->assertEquals('Unit Test Category', $section1->name);

        // Second call: retrieve same section
        $section2 = $this->invokeProtected($importer, 'get_or_create_section', [&$row]);
        $this->assertEquals($section1->id, $section2->id);
    }

    /**
     * Helper to test resource import for allowed types.
     *
     * @param string $ext                File extension and fixture name prefix.
     * @param string $expectedThumbnail  Expected thumbnail filename.
     */
    protected function runResourceImportTest(string $ext, string $expectedThumbnail): void {
        global $DB, $CFG;

        $filename = "sample.{$ext}";
        $codmidia = random_int(3000, 3999);

        // Determine codtipomidia based on extension
        $codtipomidia = match ($ext) {
            'docx','pdf','xlsx','pptx'   => midia_importer::EADTECH_DOCUMENT_FILE,
            'zip'   => midia_importer::EADTECH_OTHER,
            'mp3'   => midia_importer::EADTECH_AUDIO_FILE,
            'png'   => midia_importer::EADTECH_IMAGE_FILE,
            default => throw new coding_exception("Invalid extension"),
        };

        $row = [
            'codmidia'               => $codmidia,
            'codtipomidia'           => $codtipomidia,
            'nome'                   => "Test {$ext} Resource",
            'descricao'              => "A sample {$ext} import",
            'caminho'                => $filename,
            'categoria'              => 'Media',
            'codcategoria'           => 9,
            'exibiremmidiasdigitais' => 1,
            'datacriacao'            => date('c'),
            'imagemexibicao'         => '',
        ];

        require_once($CFG->dirroot . '/admin/tool/migration/classes/importers/midia_importer.php');
        $importer = new midia_importer($this->basepath, $this->course->id);
        $this->assertTrue(
            $importer->import_from_csv($row),
            ucfirst($ext) . ' import should succeed'
        );

        $cm = $DB->get_record(
            'course_modules',
            ['course' => $this->course->id, 'idnumber' => "eadt-md-{$codmidia}"],
            '*', MUST_EXIST
        );

        $cmid      = $cm->id;
        $imagefile = cm_image::get_file($cmid);


        $this->assertEquals(
            $expectedThumbnail,
            $imagefile->get_filename(),
            "{$ext} thumbnail should be {$expectedThumbnail}"
        );

        $context = context_module::instance($cmid);
        $files = get_file_storage()->get_area_files($context->id, 'mod_resource', 'content', 0, 'sortorder', false);

        $found = false;
        foreach ($files as $f) {
            if ($f->get_filename() === $filename) {
                $found = true;
                break;
            }
        }
        $this->assertTrue($found, "{$filename} must be present in draft area");
    }

    /** @group !current */
    public function test_import_docx(): void {
        $this->runResourceImportTest('docx', 'docx.png');
    }

    /** @group !current */
    public function test_import_pdf(): void {
        $this->runResourceImportTest('pdf', 'pdf.png');
    }

    /** @group !current */
    public function test_import_pptx(): void {
        $this->runResourceImportTest('pptx', 'pptx.png');
    }

    /** @group !current */
    public function test_import_xlsx(): void {
        $this->runResourceImportTest('xlsx', 'xlsx.png');
    }

    /** @group !current */
    public function test_import_audio(): void {
        $this->runResourceImportTest('mp3', 'audio.png');
    }

    /**
     * @group !current
     */
    public function test_import_supervideo(): void {
        global $DB, $CFG;

        $row = [
            'codmidia'               => 2002,
            'codtipomidia'           => midia_importer::EADTECH_VIDEO_FILE,
            'nome'                   => 'Test Supervideo',
            'descricao'              => 'An embedded Vimeo video',
            'caminho'                => 'https://vimeo.com/75078762',
            'categoria'              => 'Videos',
            'codcategoria'           => 8,
            'exibiremmidiasdigitais' => 1,
            'datacriacao'            => date('c'),
            'imagemexibicao'         => '',
        ];

        require_once($CFG->dirroot . '/admin/tool/migration/classes/importers/midia_importer.php');
        $importer = new midia_importer($this->basepath, $this->course->id);
        $this->assertTrue($importer->import_from_csv($row), 'Supervideo import should succeed');

        $cm = $DB->get_record('course_modules', [
            'course'   => $this->course->id,
            'idnumber' => 'eadt-md-2002',
        ], '*', MUST_EXIST);

        $cmid      = $cm->id;
        $imagefile = cm_image::get_file($cmid);
        $this->assertEquals('video.png', $imagefile->get_filename());

        $instance = $DB->get_record('supervideo', ['id' => $cm->instance], '*', MUST_EXIST);
        $this->assertEquals('https://vimeo.com/75078762', $instance->videourl);
    }
}
