<?php

require_once('../../config.php');

$context = \context_system::instance();

require_login();
require_capability('local/catalog:view', $context);

$action = optional_param('action', '', PARAM_TEXT);
$view = optional_param('view', '', PARAM_TEXT); // view
$search = optional_param('search', '', PARAM_TEXT); // Search
$parentcategory = optional_param('parentcategory', 0, PARAM_INT); // Parent Category id
$categoryid = optional_param('categoryid', 0, PARAM_INT); // Category id
$filtercategory = optional_param('jump', '', PARAM_URL);
$page = optional_param('page', 0, PARAM_INT);

// $PAGE->requires->css(new moodle_url('/local/catalog/styles.css'));

$url = new moodle_url('/local/catalog/index.php');
$title = get_string('pluginname', 'local_catalog');

$PAGE->set_context($context);
$PAGE->set_url(new moodle_url($url));
$PAGE->set_pagelayout('coursecategory');
$PAGE->set_title($title);
$PAGE->set_heading($title);

$PAGE->navbar->add($title, new moodle_url($url));

if ($categoryid) {
    $PAGE->set_category_by_id($categoryid);

    if ($action) {
        $category = \core_course_category::get($categoryid, MUST_EXIST, true);
    }
}

$redirectback = false;
$redirectmessage = false;
$type = \core\output\notification::NOTIFY_INFO;

switch ($action) {
    case 'showcategory':
        require_capability('tool/coursemanagement:edit', $context);
        required_param('categoryid', PARAM_INT);
        $redirectback = \core_course\management\helper::action_category_show($category);
        if ($parentcategory) {
            $url->params(["categoryid" => $parentcategory]);
        }
        break;
    case 'hidecategory':
        require_capability('tool/coursemanagement:edit', $context);
        required_param('categoryid', PARAM_INT);
        $redirectback = \core_course\management\helper::action_category_hide($category);
        if ($parentcategory) {
            $url->params(["categoryid" => $parentcategory]);
        }
        break;
}

if ($redirectback) {
    if ($redirectmessage) {
        redirect($url, $redirectmessage, null, $type);
    } else {
        redirect($url);
    }
}

if ($view) {
    set_user_preference('local_catalog_view', $view);
}

if ($filtercategory) {
    redirect($filtercategory);
}

if ($categoryid || $search) {
    $pagerender = new \local_catalog\output\courses($page, $categoryid, $search);
} else {
    $pagerender = new \local_catalog\output\categories($page, $categoryid, $search);
}

echo $OUTPUT->header();
echo ($PAGE->get_renderer('local_catalog'))->render($pagerender);
echo $OUTPUT->footer();
