{{!
	{
        "aboutcourse_data": [
            {
                "index": 0,
                "shortname": "",
                "name": "",
                "data": "",
                "term": {
                    "accepted": false,
                    "tag": ""
                }
            }
        ]
    }
}}

<li id="section-about-{{ shortname }}" class="section course-section main clearfix border-bottom w-100 py-1" data-id="{{ shortname }}" data-number="{{ index }}">

    <div class="course-section-header d-flex" data-for="section_title" data-id="{{ shortname }}" data-number="{{ index }}">

        <div class="d-flex align-items-start position-relative custom-link">
            <a role="button" data-toggle="collapse" data-for="sectiontoggler" href="#coursecontentcollapseabout{{ shortname }}-{{ course_unique_id }}" id="collapssesectionabout{{ shortname }}"
                class="icons-collapse-expand justify-content-center collapsed"
                aria-expanded="false"
                aria-controls="coursecontentcollapseabout{{ shortname }}-{{ course_unique_id }}" aria-label="Campo extra">

                <span class="expanded-icon icon-no-margin p-2" title="{{#str}} collapse, core {{/str}}">
                    {{#pix}} t/expandedchevron, core {{/pix}}
                </span>
                <span class="collapsed-icon icon-no-margin p-2" title="{{#str}} expand, core {{/str}}">
                    <span class="dir-rtl-hide">{{#pix}} t/collapsedchevron, core {{/pix}}</span>
                    <span class="dir-ltr-hide">{{#pix}} t/collapsedchevron_rtl, core {{/pix}}</span>
                </span>

                <h6 class="sectionname course-content-item d-flex align-self-stretch align-items-center ml-3 mb-0"
                    id="sectionid-about-title" data-for="section_title" data-id="{{ shortname }}" data-number="{{ index }}">
                    {{ name }}
                    {{# term }}
                        <span class="ml-3">{{& tag }}</span>
                    {{/ term }}
                </h6>
            </a>
            
        </div>
    </div>

    <div id="coursecontentcollapseabout{{ shortname }}-{{ course_unique_id }}" class="content course-content-item-content collapse">
        <div class="my-3 px-2">
            {{{ data }}}
        </div>
    </div>
</li>
