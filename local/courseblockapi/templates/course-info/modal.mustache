<div class="modal fade course-info-modal" id="course-info-modal-{{ course_unique_id }}" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-body pt-0">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>

                <div class="course-info mb-3">
                    <div class="courseimage overflow-hidden">
                        {{#cover}}
                            <img class="card-img-top {{#visible}} visible {{/visible}}" src="{{image}}" alt="{{course}}" 
                            />
                            {{^card}}
                                {{#logo}}
                                    <div class="card-img-overlay p-0 d-flex align-items-center justify-content-center {{#visible}}visible{{/visible}}">
                                        <div class="card-content d-flex justify-content-center p-4">
                                            <img src="{{ logo }}" alt="{{ course }}" class="card-logo w-75" />
                                        </div>
                                    </div>
                                {{/logo}}
                            {{/card}}
                        {{/cover}}
                    </div>
    
                    <div class="courseinfo-box">
                        <div class="d-flex align-items-center mb-3">
                            {{#category}}
                                <div class="card-course-category d-flex align-items-center mr-2">
                                    <i class="fa fa-tags pr-1"></i> 
                                    <span class="course-category text-truncate m-0">{{ category }}</span>
                                </div>
                            {{/category}}
    
                            {{#modality}}
                                <span class="course-modality badge badge-primary rounded-sm mr-2">
                                    {{ modality }}
                                </span>
                            {{/modality}}
    
                            {{#gamification}}
                                <span class="course-gamification badge badge-warning rounded-sm mr-2">
                                    <i class="fa-solid fa-trophy"></i> {{points}}
                                </span>
                            {{/gamification}}
                        </div>
    
                        <div class="mb-3">
                            <h2 class="card-title m-0 text-uppercase">{{ course }}</h2>
                            {{^course}}
                                <h2 class="card-title m-0 text-uppercase">{{ fullname }}</h2>
                            {{/course}}
                        </div>
    
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <div class="d-flex">
                                <a href="{{ url }}" class="card-action-link card-action-link--light card-play-link mr-2">
                                    <span class="d-flex justify-content-center align-items-center w-100">
                                        {{#pix}}i/play, local_courseblockapi{{/pix}} <span class="ml-2">{{#str}} play, local_courseblockapi {{/str}}</span>
                                    </span>
                                </a>
                          
                                <span
                                    class="card-action-link mr-2 course-favourite d-flex justify-content-center"
                                    data-courseid="{{courseid}}" data-favourite="{{#is_favourite}} 1 {{/is_favourite}} {{^is_favourite}} 0 {{/is_favourite}}">
                                    <i class="fa{{^is_favourite}}-regular{{/is_favourite}} fa-heart"
                                    ></i>
                                </span>
                            </div>
    
                            {{#showcourseprogress}}
                                <div class="d-flex align-items-center col-md-6 p-0 course-progress">
                                    <div class="progress bg-glassy w-100 mr-3" style="height: 8px;">
                                        <div class="progress-bar bg-danger" role="progressbar" style="width:{{progress_percentage}}%" aria-valuenow="{{progress_percentage}}" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                    <span class="text-white">{{progress_percentage}}%</span>
                                </div>
                            {{/showcourseprogress}}
                        </div>
    
                        <div class="d-flex align-items-center mb-3">
                            {{#hasteacher}}
                                <div class="mr-3">
                                    {{#hasmanyteachers}}
                                        <div class="dropdown">
                                            <a href="#" class="dropdown-toggle teacher-link" data-toggle="dropdown">
                                                <i class="fa fa-user pr-1"></i> {{#str}} teachers, local_courseblockapi {{/str}}
                                            </a>
                                            
                                            <div class="dropdown-menu">
                                                {{#teachers}}
                                                    <a class="dropdown-item" href="{{config.wwwroot}}/user/profile.php?id={{id}}">{{name}}</a>
                                                {{/teachers}}
                                            </div>
                                        </div>
                                    {{/hasmanyteachers}}
                                    
                                    {{^hasmanyteachers}}
                                        <a class="teacher-link" href="{{config.wwwroot}}/user/profile.php?id={{teachers.0.id}}"><i class="fa fa-user pr-1"></i> {{teachers.0.name}}</a>
                                    {{/hasmanyteachers}}
                                </div>
                            {{/hasteacher}}
    
                            {{#workload}}
                                <div class="mr-3">
                                    <i class="fa-regular fa-clock pr-1"></i> {{.}}
                                </div>
                            {{/workload}}
    
                            {{#total_sections}}
                                <div class="mr-3">
                                    <i class="fa-regular fa-folder-open pr-1 mt-2 mt-md-0"></i> {{.}}
                                </div>
                            {{/total_sections}}
    
                            {{#startdate}}
                                <div class="mr-3">
                                    <i class="fa-regular fa-calendar pr-1"></i> {{.}}
                                </div>
                            {{/startdate}}
                        </div>
    
                    </div>
    
                </div>
                {{#has_about_data}}
                    <div class="course-about" id="{{ course_unique_id }}">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5>{{#str}} course_about, local_courseblockapi{{/str}}</h5>
                            <a href="#" class="btn btn-link btn-sm" data-collapse-sections="{{ course_unique_id }}">
                                {{#str}}expandall{{/str}}
                            </a>
                        </div>

                        {{#about_data }}
                            {{> local_courseblockapi/course-info/course_about_item}}
                        {{/about_data }}
                    </div>
                {{/has_about_data}}
            </div>
         </div>
    </div>
</div>
