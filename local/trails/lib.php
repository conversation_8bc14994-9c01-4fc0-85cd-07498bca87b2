<?php

require_once($CFG->dirroot . '/local/trails/setup.php');

/**
 * Executes at the end of the setup.php.
 * The accesslib.php is already loaded at this point.
 *
 * @return void
 */
function local_trails_after_config() {}


function local_trails_reset_lang_caches(){
    get_string_manager()->reset_caches();
}



function local_trails_pluginfile($course, $cm, $context, $filearea, $args, $forcedownload, array $options=array()){
    // Check the contextlevel is as expected
    if ($context->contextlevel != CONTEXT_TRAIL) {
        return false;
    }

    // Make sure the filearea is one of those used by the plugin.
    $plugin_fileareas = [
        \local_trails\models\trail_image::IMAGE_FILEAREA,
    ];
    if (!in_array($filearea, $plugin_fileareas)) {
        return false;
    }

    // Make sure the user is logged
    // require_login();

    // Check the relevant capabilities
    // if (!has_capability('local/trails:view', $context)) {
    //     return false;
    // }

    // Leave this line out if you set the itemid to null in make_pluginfile_url (set $itemid to 0 instead).
    $itemid = array_shift($args); // The first item in the $args array.

    // Extract the filename / filepath from the $args array.
    $filename = array_pop($args); // The last item in the $args array.
    if (!$args) {
        $filepath = '/'; // $args is empty => the path is '/'
    } else {
        $filepath = '/'.implode('/', $args).'/'; // $args contains elements of the filepath
    }

    // Retrieve the file from the Files API.
    $fs = get_file_storage();
    $file = $fs->get_file($context->id, 'local_trails', $filearea, $itemid, $filepath, $filename);
    if (!$file) {
        return false; // The file does not exist.
    }

    // We can now send the file back to the browser - in this case with a cache lifetime of 1 day and no filtering.
    send_stored_file($file, 86400, 0, $forcedownload, $options);
}

function local_trails_add_menubar_icon(){
	global $PAGE;

	if(!get_config("local_trails", "enabled")  || isguestuser()){
		return false;
	}

	return (object)[
		"name" => get_string('pluginname', 'local_trails'),
		"icon" => 'icon-trails',
		"url" => new \moodle_url("/local/trails/index.php"),
		"active" => ($PAGE->pagetype == "local-trails-index" || $PAGE->pagetype == "local-trailscatalog-index") ? 'active' : '',
		"order" => 7
	];
}

/**
 * Shortcut to \local_trails\services\certificate_service::export_user_certificate_issues()
 *
 * Returns a paginated list of an user's issued certificates.
 *
 * @param renderer_base $output A plugin rendered must suffice
 * @param integer $userid
 * @param integer $per_page (defaults to 0, which means unlimited)
 * @param integer $page (defaults to 1)
 * @return object[] {
 *      (string)    certificatename,
 *      (string)    trailname,
 *      (int)       userid,
 *      (string)    code,
 *      (int)       timeissued,
 *      (string)    download_url,
 *      (string)    linkedin_url,
 *      (string)    image_url,
 * }
 */
function local_trails_export_user_certificate_issues(renderer_base $output, int $userid, int $per_page = 0, int $page = 1) : array {
    $certificate_service = \local_trails\services_provider::get_certificate_service();
    return $certificate_service->export_user_certificate_issues($output, $userid, $per_page, $page);
}

/**
 * Shortcut to \local_trails\services\certificate_service::count_user_issues()
 *
 * Returns the total of issued certificates for an user
 *
 * @param integer $userid
 * @return integer
 */
function local_trails_count_user_certificate_issues(int $userid) : int {
    $certificate_service = \local_trails\services_provider::get_certificate_service();
    return $certificate_service->count_user_certificate_issues($userid);
}