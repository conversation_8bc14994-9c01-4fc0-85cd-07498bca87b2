<?php

defined('MOODLE_INTERNAL') || die();

function local_trailscatalog_add_menubar_icon()
{
	global $PAGE;

	if (!get_config("local_trailscatalog", "enableplugin") || isguestuser()) {
		return false;
	}

	if (!has_capability('local/trailscatalog:view', \context_system::instance())) {
		return false;
	}

	return (object)[
		"name" => get_string('pluginname', 'local_trailscatalog'),
		"icon" => 'icon-trails',
		"url" => new \moodle_url("/local/trailscatalog/index.php"),
		"active" => $PAGE->pagetype == "local-trailscatalog" ? 'active' : '',
		"order" => 3
	];
}
