<?php

namespace block_mycoursetabs\external;

use \StdClass;
use \context_system;
use \core_external\external_api;
use \core_external\external_function_parameters;
use \core_external\external_multiple_structure;
use \core_external\external_single_structure;
use \core_external\external_value;
use \invalid_parameter_exception;
use \block_mycoursetabs\external\abstract_paginated_api;
use \block_mycoursetabs\external\exporter\course_card_exporter;
use tool_lfxp\util\query_object;
use \tool_usercoursestatus\utils\constants as status_constants;
use \local_offermanager\constants as offermanager_constants;

class all_courses_api extends abstract_paginated_api
{

    /**
     * Defines the structure of the filters that can be applied to the web service that returns all courses.
     *
     * @return external_single_structure
     */
    protected static function define_filters_structure(): external_single_structure
    {
        return new external_single_structure([
            'categoryid'  => new external_value(PARAM_INT, 'Category ID', VALUE_DEFAULT, 0),
            'favourite'   => new external_value(PARAM_BOOL, 'Only favourite course', VALUE_DEFAULT, false),
            'uf'   => new external_value(PARAM_TEXT, 'UF', VALUE_DEFAULT, 'all'),
            'solution_format'    => new external_value(PARAM_TEXT, 'Course solution format filter', VALUE_DEFAULT, 'all'),
            'search'      => new external_value(PARAM_TEXT, 'Search by fullname or shortname of the course', VALUE_DEFAULT, ''),
            'most_accessed' => new external_value(PARAM_BOOL, 'Order by most accessed courses', VALUE_DEFAULT, false),
        ], VALUE_DEFAULT, []);
    }

    /**
     * Returns description of get_all_courses() parameters.
     *
     * @return external_function_parameters
     */
    public static function get_all_courses_parameters()
    {
        return self::append_common_pagination_parameters([
            'filters' => self::define_filters_structure(),
        ]);
    }

    /**
     * Describes the return structure of the service.
     *
     * @return external_single_structure
     */
    public static function get_all_courses_returns()
    {
        return self::append_common_pagination_returns([
            'items' => new external_multiple_structure(
                course_card_exporter::get_read_structure(VALUE_OPTIONAL),
                'List of courses',
                VALUE_DEFAULT,
                []
            ),
            'filters' => self::define_filters_structure(),
            'query'   => new external_value(PARAM_RAW, 'Debug query info', VALUE_OPTIONAL),
        ]);
    }

    /**
     * Get a list of all courses that the user can see.
     *
     * @param int $page The page to return.
     * @param int $per_page The number of courses to return.
     * @param array $filters An associative array of filters to apply to the query.
     *      Available filters are:
     *      - categoryid: The category ID to filter by.
     *      - favourite: Whether or not to only return favourite courses.
     *      - uf: The UF to filter by.
     *      - solution_format: The course solution format to filter by.
     *      - search: A search query to filter by.
     *      - most_accessed: Whether or not to order by most accessed courses.
     * @return array An associative array of data 
     */
    public static function get_all_courses(int $page = 1, int $per_page = 16, array $filters = [])
    {
        global $OUTPUT, $USER, $PAGE, $DB;

        if ($page < 1) {
            $page = 1;
        }

        $courses = [];
        $params = compact('page', 'per_page', 'filters');
        $params = self::validate_parameters(self::get_all_courses_parameters(), $params);

        $context = context_system::instance();
        require_capability('moodle/category:viewcourselist', $context);
        $PAGE->set_context($context);

        $renderer = $PAGE->get_renderer('block_mycoursetabs');

        $query = self::build_query($params['filters']);
        $recordset = $query->get_recordset(($page - 1) * $per_page, $per_page);

        foreach ($recordset as $record) {
            $record->course_unique_id = \core\uuid::generate();
            $exporter = new course_card_exporter($record, ['userid' => (int) $USER->id]);
            $courses[] = $exporter->export($renderer);
        }

        $recordset->close();
        $total = $query->count();

        return [
            'page'        => $page,
            'per_page'    => $per_page,
            'total'       => $total,
            'total_pages' => ceil($total / $per_page),
            'show_all'    => $total > COUNT($courses),
            'items'     => $courses,
            'empty_url'   => !$total ? $OUTPUT->image_url('courses', 'block_myoverview')->out() : '',
            'filters'     => $params['filters'],
            'query' => json_encode($query),
        ];
    }

    /**
     * Build a query object to fetch all courses from the audience's courses that the user is not enrolled in.
     *
     * @param array $filters Array of filters to apply.
     * @return query_object
     */
    protected static function build_query($filters): query_object
    {
        global $USER, $DB;

        $query = new query_object();

        $query->fields = "
            mc.id,
            mlam.userid,
            mc.fullname,
            mcc.id AS categoryid,
            mcc.name AS category,
            mf.id IS NOT NULL AS is_favorite,
            0 AS showcourseprogress,
            COALESCE(SUM(mcdv.view_counter), 0) AS access_count";

        $query->from = "{local_audience_members} mlam
            JOIN {local_offermanager_audience} mloa ON mloa.audienceid = mlam.audienceid 
            JOIN {local_offermanager} mlo ON mlo.id = mloa.offerid
            JOIN {local_offermanager_course} mloc ON mloc.offerid = mlo.id
            JOIN {local_offermanager_class} mlocl ON mlocl.offercourseid = mloc.id
            JOIN {course} mc ON mc.id = mloc.courseid AND mc.visible = 1
            JOIN {course_categories} mcc ON (mcc.id = mc.category AND mcc.visible = 1)
            LEFT JOIN {local_custom_fields_course} mlcfc ON (mlcfc.courseid = mc.id)
            LEFT JOIN {favourite} mf ON (mf.itemid = mc.id AND mf.userid = mlam.userid AND mf.itemtype = 'courses')
            LEFT JOIN {courseviews_daily_views} mcdv ON mcdv.courseid = mc.id";

        $query->add_where_condition("mlam.userid = :userid");
        $query->add_where_condition("NOT EXISTS (
            SELECT 1 
            FROM {local_offermanager_ue} ue
            WHERE ue.courseid = mc.id AND ue.userid = mlam.userid
        )"); // Does not have enrollment
        $query->add_where_condition("mlo.status = 1"); // Offer
        $query->add_where_condition("mloc.status = 1"); // Offer course
        $query->add_where_condition("mlocl.status = 1"); // Offer class
        $query->add_where_condition("(mcdv.id IS NULL OR mcdv.viewing_date >= UNIX_TIMESTAMP(CURDATE() - INTERVAL :interval DAY))");

        $params = [];

        $params['userid'] = $USER->id;
        $params['interval'] = 30;

        if (!empty($filters['favourite'])) {
            $query->add_where_condition('mf.id IS NOT NULL');
        }
        if (!empty($filters['categoryid'])) {
            $catid = $filters['categoryid'];
            $like_path_sql = $DB->sql_like('mcc.path', ':catpath');
            $eq_id_sql = 'mcc.id = :catid';
            $query->add_where_condition("($like_path_sql OR $eq_id_sql)");
            $params['catpath'] = '%/' . $catid . '/%';
            $params['catid'] = $catid;
        }
        if (!empty($filters['uf']) && $filters['uf'] !== 'all') {
            $ufcondition = $DB->sql_like('mlcfc.course_uf', ':uf', false);
            $query->add_where_condition($ufcondition);
            $params['uf'] = '%' . trim($filters['uf']) . '%';
        }
        if (!empty($filters['solution_format']) && $filters['solution_format'] !== 'all') {
            $query->add_where_condition($DB->sql_like('mlcfc.solution_format', ':solution_format'));
            $params['solution_format'] = '%' . trim($filters['solution_format']) . '%';
        }
        if (!empty($filters['search'])) {
            $searchcondition = $DB->sql_like('mc.fullname', ':searchtext', false);
            $query->add_where_condition($searchcondition);
            $params['searchtext'] = '%' . trim($filters['search']) . '%';
        }

        $query->group = "mc.id";

        $query->params = $params;

        // Ordenação.pelos mais acessados(filtro)
        if (!empty($filters['most_accessed'])) {
            $query->order = 'access_count DESC, mc.fullname ASC';
        } else {
            $query->order = 'mc.fullname ASC';
        }

        return $query;
    }
}
