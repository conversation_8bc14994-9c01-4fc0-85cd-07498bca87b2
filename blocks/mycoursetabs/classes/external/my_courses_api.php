<?php

namespace block_mycoursetabs\external;

use \StdClass;
use \context_system;
use \core_external\external_api;
use \core_external\external_function_parameters;
use \core_external\external_multiple_structure;
use \core_external\external_single_structure;
use \core_external\external_value;
use \invalid_parameter_exception;
use \block_mycoursetabs\external\abstract_paginated_api;
use \block_mycoursetabs\external\exporter\course_card_exporter;
use tool_lfxp\util\query_object;
use \tool_usercoursestatus\utils\constants as status_constants;
use \local_offermanager\constants as offermanager_constants;

class my_courses_api extends abstract_paginated_api
{

    protected static function define_filters_structure(): external_single_structure
    {
        return new external_single_structure([
            'categoryid'      => new external_value(PARAM_INT, 'Category ID', VALUE_DEFAULT, 0),
            'favourite'       => new external_value(PARAM_BOOL, 'Only favourite course', VALUE_DEFAULT, false),
            'recommended'     => new external_value(PARAM_BOOL, 'Only recommended course', VALUE_DEFAULT, true),
            'uf'              => new external_value(PARAM_TEXT, 'UF', VALUE_DEFAULT, 'all'),
            'solution_format' => new external_value(PARAM_TEXT, 'Course solution format filter', VALUE_DEFAULT, 'all'),
            'search'          => new external_value(PARAM_TEXT, 'Search by fullname or shortname of the course', VALUE_DEFAULT, ''),
            'status'          => new external_value(PARAM_INT, 'Enrolment status', VALUE_DEFAULT, -1),
            'status_progress' => new external_value(PARAM_INT, 'progress status', VALUE_DEFAULT, -1),
        ], VALUE_DEFAULT, []);
    }

    public static function get_my_courses_parameters()
    {
        return self::append_common_pagination_parameters([
            'filters' => self::define_filters_structure(),
        ]);
    }

    public static function get_my_courses(int $page = 1, int $per_page = 16, array $filters = [])
    {
        global $OUTPUT, $USER, $PAGE;

        if ($page < 1) {
            $page = 1;
        }

        $courses = [];
        $params = compact('page', 'per_page', 'filters');
        $params = self::validate_parameters(self::get_my_courses_parameters(), $params);

        $context = context_system::instance();
        require_capability('moodle/category:viewcourselist', $context);
        $PAGE->set_context($context);

        $renderer = $PAGE->get_renderer('block_mycoursetabs');

        $query = self::build_query($params['filters']);
        $recordset = $query->get_recordset(($page - 1) * $per_page, $per_page);

        foreach ($recordset as $record) {
            $record->course_unique_id = \core\uuid::generate();
            $exporter = new course_card_exporter($record, ['userid' => (int) $USER->id]);
            $courses[] = $exporter->export($renderer);
        }

        $recordset->close();
        $total = $query->count();

        return [
            'page' => $page,
            'per_page' => $per_page,
            'total' => $total,
            'total_pages' => ceil($total / $per_page),
            'show_all' => $total > COUNT($courses),
            'items' => $courses,
            'empty_url' => !$total ? $OUTPUT->image_url('courses', 'block_myoverview')->out() : '',
            'filters' => $params['filters'],
            'query' => json_encode($query),
        ];
    }


    public static function get_my_courses_returns()
    {
        return self::append_common_pagination_returns([
            'items' => new external_multiple_structure(
                course_card_exporter::get_read_structure(VALUE_OPTIONAL),
                'List of courses',
                VALUE_DEFAULT,
                []
            ),
            'filters' => self::define_filters_structure(),
            'query' => new external_value(PARAM_RAW, 'Debug query info', VALUE_OPTIONAL),
        ]);
    }


    protected static function build_query($filters): query_object
    {
        global $USER, $DB;

        $params = [];
        $query = new query_object();

        $query->fields = "
            mc.id,
            mlou.userid,
            mc.fullname,
            mcc.id AS categoryid,
            mcc.name AS category,
            mf.id IS NOT NULL AS is_favorite,
            mlou.situation,
            1 AS showcourseprogress";

        $query->from = "
            {local_offermanager} mlo 
	        JOIN {local_offermanager_course} mloc ON mloc.offerid = mlo.id
	        JOIN {local_offermanager_class} mlocl ON mlocl.offercourseid = mloc.id
	        JOIN {local_offermanager_ue} mlou ON mlou.offerclassid = mlocl.id
	        JOIN {course} mc ON mc.id = mloc.courseid
	        JOIN {course_categories} mcc ON (mcc.id = mc.category AND mcc.visible = 1)
	        LEFT JOIN {local_custom_fields_course} mlcfc ON (mlcfc.courseid = mc.id)
            LEFT JOIN {favourite} mf ON (mf.itemid = mc.id AND mf.userid = mlou.userid AND mf.itemtype = 'courses')";

        $query->add_where_condition("mlou.userid = :userid");
        $query->add_where_condition("mc.visible = 1");

        //filtro "Indicados para mim"
        if (!empty($filters['recommended'])) {
            $query->add_where_condition("EXISTS (
                SELECT 1
                FROM {local_offermanager_audience} loa
                    JOIN {local_audience_members} lam ON lam.audienceid = loa.audienceid
                WHERE loa.offerid  = mlo.id
                    AND lam.userid = mlou.userid
            )");
        } else {
            $query->add_where_condition("NOT EXISTS (
                SELECT 1
                FROM {local_offermanager_audience} loa
                    JOIN {local_audience_members} lam ON lam.audienceid = loa.audienceid
                WHERE loa.offerid  = mlo.id
                    AND lam.userid = mlou.userid
            )");
        }

        // Parâmetros
        $params = array_merge($params, [
            'userid' => $USER->id,
        ]);

        // Ordenação padrão
        $query->order = 'mc.fullname ASC';

        // --- FILTROS ---
        if (!empty($filters['favourite'])) {
            $query->add_where_condition('mf.id IS NOT NULL');
        }

        if (isset($filters['status']) && $filters['status'] != -1) {
            $query->add_where_condition('mlou.situation = :situation');
            $params['situation'] = $filters['status'];
        }

        if (isset($filters['status_progress']) && $filters['status_progress'] != -1) {
            switch ($filters['status_progress']) {
                case 0:
                    $situations = [offermanager_constants::OFFER_USER_ENROL_SITUATION_ENROLED];

                    list($status_in_sql, $status_in_params) = $DB->get_in_or_equal($situations, SQL_PARAMS_NAMED, 'status');
                    $query->add_where_condition("mlou.situation $status_in_sql");
                    $params = array_merge($params, $status_in_params);
                    break;
                case 1:
                    $situations = [offermanager_constants::OFFER_USER_ENROL_SITUATION_IN_PROGRESS];

                    list($status_in_sql, $status_in_params) = $DB->get_in_or_equal($situations, SQL_PARAMS_NAMED, 'status');
                    $query->add_where_condition("mlou.situation $status_in_sql");
                    $params = array_merge($params, $status_in_params);
                    break;
                case 2:
                    $situations = [
                        offermanager_constants::OFFER_USER_ENROL_SITUATION_APPROVED,
                        offermanager_constants::OFFER_USER_ENROL_SITUATION_COMPLETED,
                        offermanager_constants::OFFER_USER_ENROL_SITUATION_USER_CANCELED,
                        offermanager_constants::OFFER_USER_ENROL_SITUATION_ADMIN_CANCELED,
                        offermanager_constants::OFFER_USER_ENROL_SITUATION_FAILED,
                        offermanager_constants::OFFER_USER_ENROL_SITUATION_NOT_COMPLETED,
                        offermanager_constants::OFFER_USER_ENROL_SITUATION_ABANDONED,
                    ];

                    list($status_in_sql, $status_in_params) = $DB->get_in_or_equal($situations, SQL_PARAMS_NAMED, 'status');
                    $query->add_where_condition("mlou.situation $status_in_sql");
                    $params = array_merge($params, $status_in_params);
                    break;
            }
        }

        if (!empty($filters['categoryid'])) {
            $catid = $filters['categoryid'];
            $like_path_sql = $DB->sql_like('mcc.path', ':catpath');
            $eq_id_sql = 'mcc.id = :catid';
            $query->add_where_condition("($like_path_sql OR $eq_id_sql)");
            $params['catpath'] = '%/' . $catid . '/%';
            $params['catid'] = $catid;
        }

        if (!empty($filters['uf']) && $filters['uf'] !== 'all') {
            $ufcondition = $DB->sql_like('mlcfc.course_uf', ':uf', false);
            $query->add_where_condition($ufcondition);
            $params['uf'] = '%' . trim($filters['uf']) . '%';
        }

        if (!empty($filters['solution_format']) && $filters['solution_format'] !== 'all') {
            $query->add_where_condition($DB->sql_like('mlcfc.solution_format', ':solution_format'));
            $params['solution_format'] = '%' . trim($filters['solution_format']) . '%';
        }

        if (!empty($filters['search'])) {
            $searchcondition = $DB->sql_like('mc.fullname', ':searchtext', false);
            $query->add_where_condition($searchcondition);
            $params['searchtext'] = '%' . trim($filters['search']) . '%';
        }

        $query->group = 'mc.id';

        $query->params = $params;

        return $query;
    }
}
