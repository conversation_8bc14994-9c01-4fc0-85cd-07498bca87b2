<?php

namespace block_mycoursetabs\external\exporter;

require_once($CFG->libdir . '/completionlib.php');

use \renderer_base;
use \core\external\exporter;
use \block_mycoursetabs\external\exporter\course_card_cover_exporter;
use \local_courseblockapi\traits\course_trait;
use \block_mycoursetabs\external\exporter\course_enrol_status_exporter;
use \block_mycoursetabs\external\exporter\course_teacher_exporter;
use \block_mycoursetabs\fields\custom_course_fields;
use core_completion\progress;

use \completion_info;
use core_external\external_multiple_structure;
use local_offermanager\persistent\offer_user_enrol_model;

class course_card_exporter extends exporter
{

    use course_trait;

    /**
     * @param object $course or compatible
     * @param array $related - An optional list of pre-loaded objects related to this object.
     */
    public function __construct(object $course, $related = [])
    {
        if (empty($related['userid'])) {
            if (!empty($course->userid)) {
                $related['userid'] = (int) $course->userid;
            }
        } else {
            $related['userid'] = (int) $related['userid'];
        }

        return parent::__construct($course, $related);
    }

    /**
     * Return the list of properties.
     *
     * @return array
     */
    protected static function define_properties()
    {
        return [
            'id' => [
                'type' => PARAM_INT,
            ],
            'fullname' => [
                'type' => PARAM_RAW,
            ],
            'categoryid' => [
                'type' => PARAM_INT,
            ],
        ];
    }

    /**
     * Other properties definition.
     *
     * @return array
     */
    protected static function define_other_properties()
    {
        $properties = [
            'course_unique_id' => [
                'type' => PARAM_RAW,
                'optional' => true,
            ],
            'courseid' => [
                'type' => PARAM_RAW,
                'optional' => true,
            ],
            'category' => [
                'type' => PARAM_RAW,
                'optional' => true,
            ],
            'cover' => [
                'type' => course_card_cover_exporter::read_properties_definition(),
                'optional' => true,
            ],
            'url' => [
                'type' => PARAM_URL,
            ],
            'is_favourite' => [
                'type' => PARAM_BOOL,
                'default' => false,
            ],
            'progress_percentage' => [
                'type' => PARAM_INT,
                'default' => 0,
            ],
            'is_enrolled' => [
                'type' => PARAM_BOOL,
                'default' => false,
            ],
            'showcourseprogress' => [
                'type' => PARAM_BOOL,
                'default' => false,
            ],
            'gamification' => [
                'type' => [
                    'points' => [
                        'type' => PARAM_TEXT,
                        'optional' => true,
                    ],
                    'coins' => [
                        'type' => PARAM_INT,
                        'optional' => true,
                    ],
                ],
                'optional' => true,
            ],
            'teachers' => [
                'type' => [
                    'id' => [
                        'type' => PARAM_INT,
                        'optional' => true,
                    ],
                    'name' => [
                        'type' => PARAM_TEXT,
                        'optional' => true,
                    ],
                ],
                'multiple' => true,
                'optional' => true,
            ],
            'teachers_string' => [
                'type' => PARAM_RAW,
                'optional' => false
            ],
            'hasteacher' => [
                'type' => PARAM_BOOL,
                'default' => false
            ],
            'hasmanyteachers' => [
                'type' => PARAM_BOOL,
                'default' => false
            ],
            'completed' => [
                'type' => PARAM_BOOL,
                'default' => false,
            ],
            'total_sections' => [
                'type' => PARAM_RAW,
                'default' => '',
            ],
            'total_comments' => [
                'type' => PARAM_INT,
                'default' => 0,
            ],
            'total_new_updates' => [
                'type' => PARAM_INT,
                'default' => 0,
            ],
            'status' => [
                'type' => course_status_exporter::read_properties_definition(),
                'optional' => true,
            ],
            'recommended' => [
                'type' => PARAM_BOOL,
                'default' => false,
            ],
            'about_data' => [
                'type' => course_card_about_exporter::read_properties_definition(VALUE_OPTIONAL),
                'multiple' => true,
                'optional' => true,
            ],
            'has_about_data' => [
                'type' => PARAM_BOOL,
                'default' => false
            ]
        ];

        foreach (custom_course_fields::get_available_shortnames() as $shortname) {
            $properties[$shortname] = [
                'type' => PARAM_RAW,
                'default' => '',
            ];
        }

        return $properties;
    }

    /**
     * Assign values to the defined other properties.
     *
     * @param renderer_base $output The output renderer object.
     * @return array
     * @throws coding_exception
     * @throws dml_exception
     * @throws moodle_exception
     */
    protected function get_other_values(renderer_base $output)
    {
        $values = [];

        $userid = $this->related['userid'];
        $courseid = $this->data->id;
        $values['courseid'] = $courseid;

        $values['url'] = $this->get_course_url($courseid);
        $values['cover'] = self::get_course_image($this->data, 'object');

        $statusid = isset($this->data?->statusid) ? $this->data->statusid : 0;
        $status_exporter = course_status_exporter::instance($courseid, $userid, $statusid);
        $values['status'] = $status_exporter->export($output);

        if (!isset($this->data->total_sections)) {
            $values['total_sections'] = self::get_course_total_sections($courseid);
        }

        if (!isset($this->data->total_comments)) {
            $values['total_comments'] = self::get_course_total_comments($courseid);
        }

        if (!isset($this->data->gamification)) {
            $values['gamification'] = self::get_course_gamification($courseid);
        }

        if (!isset($this->data->teachers)) {
            $values['teachers'] = self::get_course_teachers($courseid, false);
        }

        if (!isset($this->data->teachers_string)) {
            $values['teachers_string'] = self::make_teacher_string($values['teachers']);
        }

        if (!isset($this->data->hasteacher)) {
            $values['hasteacher'] = count($values['teachers']) > 0;
        }

        if (!isset($this->data->hasmanyteachers)) {
            $values['hasmanyteachers'] = count($values['teachers']) > 1;
        }

        if (!isset($this->data->is_favourite)) {
            $values['is_favourite'] = self::get_course_is_favourite($courseid, $userid);
        }

        if (!isset($this->data->is_enrolled)) {
            $values['is_enrolled'] = self::get_course_user_is_enrolled($courseid, $userid);
        }

        if (!isset($this->data->completed)) {
            $completion_info = new completion_info($this->data);
            $values['completed'] = $completion_info->is_course_complete($userid);
        }

        if (!isset($this->data->progress_percentage)) {
            $progress = offer_user_enrol_model::calculate_course_progress($userid, $this->data) ?: 0;
            $values['progress_percentage'] = round($progress);
        } else {
            $values['progress_percentage'] = round($this->data->progress_percentage);
        }

        if (!isset($this->data->category)) {
            $values['category'] = $this->get_category_name();
        }

        if (!isset($this->data->about_data)) {
            $values['about_data'] = self::get_aboutcourse_data($courseid);
        }

        if (!isset($this->data->has_about_data)) {
            $values['has_about_data'] = !empty($values['about_data']);
        }

        $course_custom_fields = $this->get_course_custom_fields($courseid);
        foreach (custom_course_fields::get_available_shortnames() as $shortname) {
            if (empty($this->data->$shortname) && !empty($course_custom_fields->$shortname)) {
                $values[$shortname] = $course_custom_fields->$shortname;
            }
        }

        return $values;
    }

    protected function get_category_name(): string
    {
        global $DB;

        try {
            return $DB->get_field('course_categories', 'name', ['id' => $this->data?->categoryid]) ?: '';
        } catch (\Throwable $th) {
            return '';
        }
    }

    /**
     * Defines related information.
     *
     * @return array
     */
    protected static function define_related()
    {
        return array(
            'userid' => 'int',
        );
    }
}
