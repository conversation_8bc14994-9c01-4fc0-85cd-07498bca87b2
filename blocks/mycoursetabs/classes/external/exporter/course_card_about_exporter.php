<?php

namespace block_mycoursetabs\external\exporter;

use \core\external\exporter;

class course_card_about_exporter extends exporter
{

    /**
     * Return the list of properties.
     *
     * @return array
     */
    protected static function define_properties()
    {
        return [
            'index' => [
                'type' => PARAM_INT,
                'default' => '',
            ],
            'shortname' => [
                'type' => PARAM_TEXT,
                'default' => '',
            ],
            'name' => [
                'type' => PARAM_TEXT,
                'default' => '',
            ],
            'data' => [
                'type' => PARAM_RAW,
                'default' => '',
            ],
            'term' => [
                'type' => [
                    'accepted' => [
                        'type' => PARAM_BOOL,
                        'default' => false
                    ],
                    'tag' => [
                        'type' => PARAM_TEXT,
                        'null' => NULL_ALLOWED,
                    ],
                ],
                'optional' => true,
            ],
        ];
    }
}
