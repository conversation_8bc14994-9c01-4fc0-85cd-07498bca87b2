<a id="emcurso"></a>
<div class="local_courseblockapi mycoursetabs">
	<div class="d-lg-flex align-items-center">
		<h2 class="mb-2 mb-lg-0 mr-2 blocktitle">
			Soluções 
			<i class="fa fa-chevron-right pl-1 fa-sm d-none d-lg-inline"></i>
			<i class="fa fa-chevron-down pl-1 fa-sm d-lg-none d-inline"></i>
		</h2>
		<ul class="nav nav-pills" role="tablist">
			{{#tabs}}
			<li class="nav-item" role="presentation" style="flex:0 0 auto">
				<button class="nav-link tablink {{#active}}active{{/active}}" id="tab-{{name}}" data-toggle="pill" data-target="#{{name}}" type="button" role="tab" aria-controls="{{name}}">
					{{label}}
				</button>
			</li>
			{{/tabs}}
		</ul>
	</div>

	<div class="tab-content position-relative mt-2">
		{{> block_mycoursetabs/block-loading}}

		<div id="mycoursetabs-filters" class="filters mb-3">
			<!-- Dynamically created -->
		</div>

		{{#tabs}}
			<div class="tab-pane fade {{#active}}show active{{/active}}" id="{{name}}" role="tabpanel" aria-labelledby="tab-{{name}}">
				<div class="cards">
					<!-- Dynamically created -->
				</div>
			</div>
		{{/tabs}}

		<div class="courses-page-footer container">
			<div class="pagination-wrapper d-flex justify-content-center mt-5">
				<!-- Dynamically created -->
			</div>
		</div>
	</div>
</div>