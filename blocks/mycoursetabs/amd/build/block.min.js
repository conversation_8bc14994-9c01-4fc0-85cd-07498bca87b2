define("block_mycoursetabs/block",["exports","jquery","core/ajax","core/templates","core/notification","local_courseblockapi/api","theme_boost/bootstrap/tooltip","core/str","local_courseblockapi/favourite"],(function(_exports,_jquery,_ajax,_templates,Notification,_api,_tooltip,_str,_favourite){function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=void 0,_jquery=_interopRequireDefault(_jquery),_ajax=_interopRequireDefault(_ajax),_templates=_interopRequireDefault(_templates),Notification=function(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}newObj.default=obj,cache&&cache.set(obj,newObj);return newObj}(Notification),_api=_interopRequireDefault(_api),_favourite=_interopRequireDefault(_favourite);const SELECTORS_blockContainer=".mycoursetabs",SELECTORS_filters=".mycoursetabs .filters",SELECTORS_filtersInput=".mycoursetabs .filters input, .mycoursetabs .filters select",SELECTORS_pagination=".mycoursetabs .pagination-wrapper";async function renderCourses(tab){let args=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{filters:{}},$loading=(0,_jquery.default)(SELECTORS_blockContainer+" .smart-loading"),$currentTabCards=(0,_jquery.default)(SELECTORS_blockContainer+" .tab-pane#"+tab+" .cards"),$otherTabsCards=(0,_jquery.default)(SELECTORS_blockContainer+" .tab-pane:not(#"+tab+") .cards");$loading.addClass("show"),async function(){const request={methodname:{mycourses:"block_mycoursetabs_get_my_courses",allcourses:"block_mycoursetabs_get_all_courses"}[arguments.length>0&&void 0!==arguments[0]?arguments[0]:"mycourses"],args:arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}};return _ajax.default.call([request])[0]}(tab,args).then((async data=>{let paginationData=preparePagePagination(data),cards=await _templates.default.render("block_mycoursetabs/block-cards",data),pagination=paginationData?await _templates.default.render("block_mycoursetabs/pagination",paginationData):"";$currentTabCards.html(cards),(0,_jquery.default)(SELECTORS_pagination).html(pagination),$loading.removeClass("show"),$otherTabsCards.html(""),resetCardsHeight(),(0,_jquery.default)("[data-tooltip]").tooltip(),initCollapseInModal(),_favourite.default.init("block_mycoursetabs")})).catch((error=>{console.error(error),Notification.exception()}))}_exports.init=async function(){let tab=getCurrentTab();renderCourses(tab),renderFilters(tab),(0,_jquery.default)(document).on("click",SELECTORS_blockContainer+" .tablink",(function(){let tab=(0,_jquery.default)(this).data("target").replace("#","");(0,_jquery.default)(SELECTORS_pagination).html(""),renderCourses(tab),renderFilters(tab)})).on("click",SELECTORS_blockContainer+" .btn-clear-filters",(function(e){e.preventDefault();let tab=getCurrentTab();(0,_jquery.default)(SELECTORS_filtersInput).val(""),(0,_jquery.default)(SELECTORS_filtersInput).filter(":checked").prop("checked",!1),(0,_jquery.default)(SELECTORS_filtersInput).filter("#recommended").prop("checked",!0),(0,_jquery.default)(SELECTORS_filtersInput).filter("#input-status").val("-1"),(0,_jquery.default)(SELECTORS_filtersInput).filter("#input-status-progress").val("-1"),renderCourses(tab)})).on("change",SELECTORS_blockContainer+" .filters form",(e=>{e.preventDefault();let tab=getCurrentTab(),filters=getFiltersData();console.log("filters",filters),renderCourses(tab,{filters:filters})})).on("click",SELECTORS_pagination+" .page-link",(e=>{e.preventDefault();let tab=getCurrentTab(),filters=getFiltersData();getCurrentPage();renderCourses(tab,{page:(0,_jquery.default)(this).data("page"),filters:filters})})).on("click",".card-course-favourite",(async e=>{e.preventDefault();let $button=(0,_jquery.default)(this),courseid=$button.data("courseid"),status=0==$button.data("favourite")||0==$button.data("favourite");try{const result=await _api.default.set_favourite({courses:[{component:null,id:courseid,favourite:status}]});result.warnings&&result.warnings.forEach((warning=>{Notification.addNotification({message:warning.message,type:"error"})}));let tab=getCurrentTab(),filters=getFiltersData(),page=getCurrentPage(),toggle=status?1:0;$button.data("favourite",toggle),$button.find("i").toggleClass("fa-regular",!toggle),$button.find("i").toggleClass("fa",toggle),renderCourses(tab,{page:page,filters:filters})}catch(error){Notification.addNotification({message:"Erro ao atualizar favorito.",type:"error"}),console.error(error)}})),(0,_jquery.default)(window).on("resize",resetCardsHeight)};const initCollapseInModal=async()=>{const showall=await(0,_str.get_string)("expandall"),closeall=await(0,_str.get_string)("collapseall");document.querySelectorAll("[data-collapse-sections]").forEach((button=>{button.addEventListener("click",(e=>{e.preventDefault();const targetId=button.getAttribute("data-collapse-sections"),collapses=document.getElementById(targetId).querySelectorAll(".collapse");collapses.forEach((collapse=>{collapse.removeAttribute("data-parent")}));const allOpen=Array.from(collapses).every((el=>el.classList.contains("show")));collapses.forEach((collapse=>{const collapseId=collapse.getAttribute("id"),trigger=document.querySelector('[href="#'.concat(collapseId,'"]'));allOpen?((0,_jquery.default)(collapse).collapse("hide"),trigger&&(trigger.classList.add("collapsed"),trigger.setAttribute("aria-expanded","false"))):((0,_jquery.default)(collapse).collapse("show"),trigger&&(trigger.classList.remove("collapsed"),trigger.setAttribute("aria-expanded","true")))})),button.textContent=allOpen?showall:closeall}))}))};async function renderFilters(tab){let filtered=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{const data=await async function(){const request={methodname:"block_mycoursetabs_get_filters",args:{tab:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"mycourses",filtered:arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}}};return _ajax.default.call([request])[0]}(tab,filtered),filters=await _templates.default.render("block_mycoursetabs/block-filters",{filters:data});(0,_jquery.default)(SELECTORS_filters).html(filters)}catch(error){console.error("Erro ao renderizar filtros:",error)}}const getCurrentPage=()=>(0,_jquery.default)(SELECTORS_pagination+" .page-item.active button.page-link").data("page")||1,preparePagePagination=results=>{if(null==results||!results.total_pages||1==results.total_pages)return{};let isMobileView=window.innerWidth<=600,pagination={label:"Page",pages:[],haspages:!0,pagesize:results.per_page||10},currentPage=results.page,totalPages=results.total_pages;currentPage>1&&(pagination.previous={page:currentPage-1,url:"?page=".concat(currentPage-1)}),currentPage<totalPages&&(pagination.next={page:currentPage+1,url:"?page=".concat(currentPage+1)});let treshold=0;if(totalPages<=5)for(let i=1;i<=totalPages;i++)pagination.pages.push({page:i,active:i===currentPage,url:"?page=".concat(i)});else if(currentPage<=5&&!isMobileView||currentPage<=3&&isMobileView){if(currentPage<=totalPages-5&&(treshold=2),isMobileView)for(let i=1;i<=3;i++)pagination.pages.push({page:i,active:i===currentPage,url:"?page=".concat(i)});else for(let i=1;i<=currentPage+3&&i<=totalPages-treshold;i++)pagination.pages.push({page:i,active:i===currentPage,url:"?page=".concat(i)});2==treshold||isMobileView?pagination.last={page:totalPages,active:totalPages==currentPage,url:"?page=".concat(totalPages)}:currentPage+3<totalPages&&pagination.pages.push({page:totalPages,active:totalPages===currentPage,url:"?page=".concat(totalPages)})}else if(currentPage>totalPages-5&&!isMobileView||currentPage>totalPages-3&&isMobileView){treshold=1,isMobileView?currentPage>3&&(treshold=totalPages-2,pagination.first={page:1,active:1==currentPage,url:"?page=".concat(1)}):currentPage>=6&&(treshold=currentPage-3,pagination.first={page:1,active:1==currentPage,url:"?page=".concat(1)});for(let i=treshold;i<=totalPages;i++)pagination.pages.push({page:i,active:i===currentPage,url:"?page=".concat(i)})}else{if(pagination.first={page:1,active:1==currentPage,url:"?page=".concat(1)},isMobileView)pagination.pages.push({page:currentPage,active:!0,url:"?page=".concat(currentPage)});else for(let i=currentPage-3;i<=currentPage+3;i++)pagination.pages.push({page:i,active:i===currentPage,url:"?page=".concat(i)});pagination.last={page:totalPages,active:totalPages==currentPage,url:"?page=".concat(totalPages)}}return pagination},getFiltersData=()=>{const formData={};return document.querySelector(SELECTORS_blockContainer+" .filters form").querySelectorAll("input, textarea, select").forEach((el=>{el.name&&("checkbox"===el.type?formData[el.name]=el.checked:"radio"===el.type?el.checked&&(formData[el.name]=transformData(el)):formData[el.name]=transformData(el))})),formData},transformData=data=>{switch(data.name){case"categoryid":return""!==data.value?data.value:0;case"status":case"status_progress":return""!==data.value?data.value:-1;default:return data.value}},getCurrentTab=()=>(0,_jquery.default)(SELECTORS_blockContainer+" .tablink.active").data("target").replace("#",""),resetCardsHeight=()=>{const cardSelector=SELECTORS_blockContainer+" .card",container=document.querySelector(SELECTORS_blockContainer),cards=document.querySelectorAll(cardSelector);if(!cards.length)return!1;const cardWidths=Array.from(cards).map((card=>card.scrollWidth)),new_image_height=350*Math.max(...cardWidths)/600,new_card_height=new_image_height+16;container.style.setProperty("--img-height","".concat(new_image_height,"px")),container.style.setProperty("--card-height","".concat(new_card_height,"px"));const cardBodySelector=cardSelector+" .card-body",cardBodies=document.querySelectorAll(cardBodySelector),cardBodyHeights=Array.from(cardBodies).map((body=>body.scrollHeight)),new_card_height_hover=Math.max(...cardBodyHeights)+new_image_height+5;return container.style.setProperty("--card-height-hover","".concat(new_card_height_hover,"px")),!0}}));

//# sourceMappingURL=block.min.js.map