{"version": 3, "file": "block.min.js", "sources": ["../src/block.js"], "sourcesContent": ["/* eslint-disable complexity */\n/* eslint-disable no-console */\nimport $ from \"jquery\";\nimport Ajax from \"core/ajax\";\nimport Templates from \"core/templates\";\nimport * as Notification from \"core/notification\";\nimport API from \"local_courseblockapi/api\";\nimport \"theme_boost/bootstrap/tooltip\";\nimport { get_string as getString } from \"core/str\";\nimport Favourite from \"local_courseblockapi/favourite\";\n\nconst SELECTORS = {\n  blockContainer: \".mycoursetabs\",\n  filters: \".mycoursetabs .filters\",\n  filtersInput: \".mycoursetabs .filters input, .mycoursetabs .filters select\",\n  pagination: \".mycoursetabs .pagination-wrapper\",\n  footerContainer: \".mycoursetabs .courses-page-footer\",\n};\n\n/**\n * Initializes the My Course Tabs block.\n *\n * @return {Promise.<void>} A promise that resolves when the initialization is complete.\n */\nexport const init = async function () {\n  let tab = getCurrentTab();\n\n  renderCourses(tab);\n  renderFilters(tab);\n\n  $(document)\n    .on(\"click\", SELECTORS.blockContainer + \" .tablink\", function () {\n      let tab = $(this).data(\"target\").replace(\"#\", \"\");\n      $(SELECTORS.pagination).html(\"\");\n\n      renderCourses(tab);\n      renderFilters(tab);\n    })\n    .on(\n      \"click\",\n      SELECTORS.blockContainer + \" .btn-clear-filters\",\n      function (e) {\n        e.preventDefault();\n\n        let tab = getCurrentTab();\n\n        $(SELECTORS.filtersInput).val(\"\");\n        $(SELECTORS.filtersInput).filter(\":checked\").prop(\"checked\", false);\n        $(SELECTORS.filtersInput).filter(\"#recommended\").prop(\"checked\", true);\n        $(SELECTORS.filtersInput).filter(\"#input-status\").val(\"-1\");\n        $(SELECTORS.filtersInput).filter(\"#input-status-progress\").val(\"-1\");\n\n        renderCourses(tab);\n      }\n    )\n    .on(\"change\", SELECTORS.blockContainer + \" .filters form\", (e) => {\n      e.preventDefault();\n\n      let tab = getCurrentTab();\n      let filters = getFiltersData();\n\n      console.log(\"filters\", filters);\n\n      renderCourses(tab, { filters: filters });\n    })\n    .on(\"click\", SELECTORS.pagination + \" .page-link\", (e) => {\n      e.preventDefault();\n\n      let tab = getCurrentTab();\n      let filters = getFiltersData();\n      let currentPage = getCurrentPage();\n      let pageValue = $(this).data(\"page\");\n      let page = pageValue;\n\n      renderCourses(tab, { page: page, filters: filters });\n    })\n    .on(\"click\", \".card-course-favourite\", async (e) => {\n      e.preventDefault();\n\n      let $button = $(this);\n      let courseid = $button.data(\"courseid\");\n      let status =\n        $button.data(\"favourite\") == 0 || $button.data(\"favourite\") == false;\n\n      try {\n        const result = await API.set_favourite({\n          courses: [\n            {\n              component: null,\n              id: courseid,\n              favourite: status,\n            },\n          ],\n        });\n\n        if (result.warnings) {\n          result.warnings.forEach((warning) => {\n            Notification.addNotification({\n              message: warning.message,\n              type: \"error\",\n            });\n          });\n        }\n\n        let tab = getCurrentTab();\n        let filters = getFiltersData();\n        let page = getCurrentPage();\n        let toggle = status ? 1 : 0;\n\n        $button.data(\"favourite\", toggle);\n        $button.find(\"i\").toggleClass(\"fa-regular\", !toggle);\n        $button.find(\"i\").toggleClass(\"fa\", toggle);\n\n        renderCourses(tab, { page: page, filters: filters });\n      } catch (error) {\n        Notification.addNotification({\n          message: \"Erro ao atualizar favorito.\",\n          type: \"error\",\n        });\n        console.error(error);\n      }\n    });\n\n  $(window).on(\"resize\", resetCardsHeight);\n};\n\n/**\n * Get courses for a tab\n *\n * @param {string} [tab=mycourses] The tab to get the courses for\n * @param {Object} [args={}] Additional arguments to pass to the webservice\n * @return {Promise.<Object[]>} A promise that resolves with the courses for the given tab\n */\nasync function getCourses(tab = \"mycourses\", args = {}) {\n  let method = {\n    mycourses: \"block_mycoursetabs_get_my_courses\",\n    allcourses: \"block_mycoursetabs_get_all_courses\",\n  };\n\n  const request = {\n    methodname: method[tab],\n    args: args,\n  };\n\n  return Ajax.call([request])[0];\n}\n\n/**\n * Fetches filters for a specified tab.\n *\n * @param {string} [tab=\"mycourses\"] - The tab for which filters are to be retrieved.\n * @param {Object} [filtered={}] - Additional filter criteria.\n * @return {Promise.<Object>} A promise that resolves with the filters for the specified tab.\n */\nasync function getTabFilters(tab = \"mycourses\", filtered = {}) {\n  const request = {\n    methodname: \"block_mycoursetabs_get_filters\",\n    args: {\n      tab: tab,\n      filtered: filtered,\n    },\n  };\n\n  return Ajax.call([request])[0];\n}\n\n/**\n * Renders the courses for a specified tab.\n *\n * Fetches courses using the provided tab and arguments, displays a loading indicator during the\n * fetch, and updates the UI with the retrieved course cards and pagination. Clears the course\n * cards of other tabs and adjusts card height upon completion.\n *\n * @param {string} tab - The tab for which courses are to be rendered.\n * @param {Object} [args={ filters: {} }] - Additional arguments to pass for fetching courses,\n *                                          including filters.\n * @return {Promise.<void>} A promise that resolves when the courses have been rendered.\n */\nasync function renderCourses(tab, args = { filters: {} }) {\n  let $loading = $(SELECTORS.blockContainer + \" .smart-loading\");\n  let $currentTabCards = $(\n    SELECTORS.blockContainer + \" .tab-pane#\" + tab + \" .cards\"\n  );\n  let $otherTabsCards = $(\n    SELECTORS.blockContainer + \" .tab-pane:not(#\" + tab + \") .cards\"\n  );\n\n  $loading.addClass(\"show\");\n\n  getCourses(tab, args)\n    .then(async (data) => {\n      let paginationData = preparePagePagination(data);\n\n      let cards = await Templates.render(\n        \"block_mycoursetabs/block-cards\",\n        data\n      );\n      let pagination = paginationData\n        ? await Templates.render(\n            \"block_mycoursetabs/pagination\",\n            paginationData\n          )\n        : \"\";\n\n      $currentTabCards.html(cards);\n      $(SELECTORS.pagination).html(pagination);\n\n      $loading.removeClass(\"show\");\n      $otherTabsCards.html(\"\");\n\n      resetCardsHeight();\n\n      $(\"[data-tooltip]\").tooltip();\n\n      initCollapseInModal();\n\n      Favourite.init(\"block_mycoursetabs\");\n    })\n    .catch((error) => {\n      console.error(error);\n      Notification.exception();\n    });\n}\n\n/**\n * Initializes click event listeners for elements with the 'data-collapse-sections' attribute.\n *\n * When an element is clicked, toggles the collapse state of associated sections.\n * If all sections are currently expanded, they will be collapsed, and vice versa.\n */\nconst initCollapseInModal = async () => {\n  const showall = await getString(\"expandall\");\n  const closeall = await getString(\"collapseall\");\n\n  document.querySelectorAll(\"[data-collapse-sections]\").forEach((button) => {\n    button.addEventListener(\"click\", (e) => {\n      e.preventDefault();\n\n      const targetId = button.getAttribute(\"data-collapse-sections\");\n      const target = document.getElementById(targetId);\n      const collapses = target.querySelectorAll(\".collapse\");\n\n      // Remove o atributo data-parent (caso exista)\n      collapses.forEach((collapse) => {\n        collapse.removeAttribute(\"data-parent\");\n      });\n\n      // Verifica se todos estão abertos (classe \"show\" = aberto)\n      const allOpen = Array.from(collapses).every((el) =>\n        el.classList.contains(\"show\")\n      );\n\n      collapses.forEach((collapse) => {\n        const collapseId = collapse.getAttribute(\"id\");\n        const trigger = document.querySelector(`[href=\"#${collapseId}\"]`);\n\n        if (allOpen) {\n          // Ocultar o collapse simulando clique no botão\n          $(collapse).collapse(\"hide\");\n          if (trigger) {\n            trigger.classList.add(\"collapsed\");\n            trigger.setAttribute(\"aria-expanded\", \"false\");\n          }\n        } else {\n          // Mostrar o collapse\n          $(collapse).collapse(\"show\");\n          if (trigger) {\n            trigger.classList.remove(\"collapsed\");\n            trigger.setAttribute(\"aria-expanded\", \"true\");\n          }\n        }\n      });\n\n      // Atualizar o texto do botão principal\n      button.textContent = allOpen ? showall : closeall;\n    });\n  });\n};\n\n/**\n * Renders the filters for a specified tab.\n *\n * @param {string} [tab=\"mycourses\"] - The tab for which filters are to be rendered.\n * @param {Object} [filtered={}] - Additional filter criteria.\n * @return {Promise.<void>} A promise that resolves when the filters have been rendered.\n */\nasync function renderFilters(tab, filtered = {}) {\n  try {\n    const data = await getTabFilters(tab, filtered);\n    const filters = await Templates.render(\"block_mycoursetabs/block-filters\", {\n      filters: data,\n    });\n    $(SELECTORS.filters).html(filters);\n  } catch (error) {\n    console.error(\"Erro ao renderizar filtros:\", error);\n  }\n}\n\n/**\n * Gets the current page number from the pagination buttons.\n *\n * @return {number} The current page number.\n */\nconst getCurrentPage = () => {\n  return (\n    $(SELECTORS.pagination + \" .page-item.active button.page-link\").data(\n      \"page\"\n    ) || 1\n  );\n};\n\n/**\n * Prepare the pagination object from a given results object.\n *\n * @param {object} results The results object with the following properties:\n *   - total_pages: The total number of pages.\n *   - page: The current page number.\n *   - per_page: The number of items per page.\n *\n * @return {object} The pagination object with the following properties:\n *   - label: The label to display in the pagination.\n *   - pages: An array of page objects with the following properties:\n *     - page: The page number.\n *     - active: Whether the page is currently active.\n *     - url: The URL to go to when the page is clicked.\n *   - haspages: Whether there are multiple pages.\n *   - pagesize: The number of items per page.\n */\nconst preparePagePagination = (results) => {\n  if (!results?.total_pages || results.total_pages == 1) {\n    return {};\n  }\n\n  let isMobileView = window.innerWidth <= 600; /*  */\n  let pagination = {\n    label: \"Page\",\n    pages: [],\n    haspages: true,\n    pagesize: results.per_page || 10,\n  };\n\n  let currentPage = results.page;\n  let totalPages = results.total_pages;\n\n  if (currentPage > 1) {\n    pagination.previous = {\n      page: currentPage - 1,\n      url: `?page=${currentPage - 1}`,\n    };\n  }\n\n  if (currentPage < totalPages) {\n    pagination.next = {\n      page: currentPage + 1,\n      url: `?page=${currentPage + 1}`,\n    };\n  }\n\n  let treshold = 0;\n  if (totalPages <= 5) {\n    for (let i = 1; i <= totalPages; i++) {\n      pagination.pages.push({\n        page: i,\n        active: i === currentPage,\n        url: `?page=${i}`,\n      });\n    }\n  } else {\n    if (\n      (currentPage <= 5 && !isMobileView) ||\n      (currentPage <= 3 && isMobileView)\n    ) {\n      // Primeiras páginas\n      if (currentPage <= totalPages - 5) {\n        treshold = 2;\n      }\n\n      if (!isMobileView) {\n        for (\n          let i = 1;\n          i <= currentPage + 3 && i <= totalPages - treshold;\n          i++\n        ) {\n          pagination.pages.push({\n            page: i,\n            active: i === currentPage,\n            url: `?page=${i}`,\n          });\n        }\n      } else {\n        for (let i = 1; i <= 3; i++) {\n          pagination.pages.push({\n            page: i,\n            active: i === currentPage,\n            url: `?page=${i}`,\n          });\n        }\n      }\n\n      if (treshold == 2 || isMobileView) {\n        pagination.last = {\n          page: totalPages,\n          active: totalPages == currentPage,\n          url: `?page=${totalPages}`,\n        };\n      } else if (currentPage + 3 < totalPages) {\n        pagination.pages.push({\n          page: totalPages,\n          active: totalPages === currentPage,\n          url: `?page=${totalPages}`,\n        });\n      }\n    } else if (\n      (currentPage > totalPages - 5 && !isMobileView) ||\n      (currentPage > totalPages - 3 && isMobileView)\n    ) {\n      // últimas páginas\n      treshold = 1;\n      if (isMobileView) {\n        if (currentPage > 3) {\n          treshold = totalPages - 2;\n          pagination.first = {\n            page: 1,\n            active: 1 == currentPage,\n            url: `?page=${1}`,\n          };\n        }\n      } else if (currentPage >= 6) {\n        treshold = currentPage - 3;\n        pagination.first = {\n          page: 1,\n          active: 1 == currentPage,\n          url: `?page=${1}`,\n        };\n      }\n\n      for (let i = treshold; i <= totalPages; i++) {\n        pagination.pages.push({\n          page: i,\n          active: i === currentPage,\n          url: `?page=${i}`,\n        });\n      }\n    } else {\n      // Meio\n      pagination.first = {\n        page: 1,\n        active: 1 == currentPage,\n        url: `?page=${1}`,\n      };\n      if (isMobileView) {\n        pagination.pages.push({\n          page: currentPage,\n          active: true,\n          url: `?page=${currentPage}`,\n        });\n      } else {\n        for (let i = currentPage - 3; i <= currentPage + 3; i++) {\n          pagination.pages.push({\n            page: i,\n            active: i === currentPage,\n            url: `?page=${i}`,\n          });\n        }\n      }\n      pagination.last = {\n        page: totalPages,\n        active: totalPages == currentPage,\n        url: `?page=${totalPages}`,\n      };\n    }\n  }\n  return pagination;\n};\n\n/**\n * Get the filters data from the current page.\n *\n * @return {Object} filters\n */\nconst getFiltersData = () => {\n  const formData = {};\n\n  const form = document.querySelector(\n    SELECTORS.blockContainer + \" .filters form\"\n  );\n  form.querySelectorAll(\"input, textarea, select\").forEach((el) => {\n    if (el.name) {\n      if (el.type === \"checkbox\") {\n        formData[el.name] = el.checked;\n      } else if (el.type === \"radio\") {\n        if (el.checked) {\n          formData[el.name] = transformData(el);\n        }\n      } else {\n        formData[el.name] = transformData(el);\n      }\n    }\n  });\n\n  return formData;\n};\n\n/**\n * Transforms the input data based on its name.\n *\n * @param {Object} data - The data object containing name and value properties.\n * @param {string} data.name - The name of the data field.\n * @param {string} data.value - The value of the data field.\n * @returns {string|number} - Returns the value as is for default case,\n *   or 0 if value is an empty string for \"status\" or \"categoryid\".\n */\nconst transformData = (data) => {\n  switch (data.name) {\n    case \"categoryid\":\n      return data.value !== \"\" ? data.value : 0;\n    case \"status\":\n      return data.value !== \"\" ? data.value : -1;\n    case \"status_progress\":\n      return data.value !== \"\" ? data.value : -1;\n    default:\n      return data.value;\n  }\n};\n\n/**\n * Retrieves the identifier of the currently active tab.\n *\n * Queries the DOM for the active tab link within the block container\n * and extracts the data-target attribute, returning it without the\n * leading \"#\" character.\n *\n * @return {string} The identifier of the active tab.\n */\nconst getCurrentTab = () => {\n  let $activeTab = $(SELECTORS.blockContainer + \" .tablink.active\");\n\n  return $activeTab.data(\"target\").replace(\"#\", \"\");\n};\n\n/**\n * Resets the height of cards within the block container based on their widths and original image dimensions.\n *\n * This function calculates the new height for card images and card bodies, updating CSS variables\n * to reflect these new dimensions. The calculated heights include additional pixels for styling purposes.\n *\n * @return {boolean} Returns false if no cards are found, otherwise true.\n */\nconst resetCardsHeight = () => {\n  const imgOriginalWidth = 600;\n  const imgOriginalHeight = 350;\n  const cardSelector = SELECTORS.blockContainer + \" .card\";\n  const container = document.querySelector(SELECTORS.blockContainer);\n\n  const cards = document.querySelectorAll(cardSelector);\n  if (!cards.length) return false;\n\n  const cardWidths = Array.from(cards).map((card) => card.scrollWidth);\n  const card_width = Math.max(...cardWidths);\n\n  const new_image_height = (card_width * imgOriginalHeight) / imgOriginalWidth;\n  const new_card_height = new_image_height + 16; // add 16px\n\n  container.style.setProperty(\"--img-height\", `${new_image_height}px`);\n  container.style.setProperty(\"--card-height\", `${new_card_height}px`);\n\n  const cardBodySelector = cardSelector + \" .card-body\";\n  const cardBodies = document.querySelectorAll(cardBodySelector);\n\n  const cardBodyHeights = Array.from(cardBodies).map(\n    (body) => body.scrollHeight\n  );\n  const card_body_height = Math.max(...cardBodyHeights);\n\n  const new_card_height_hover = card_body_height + new_image_height + 5;\n  container.style.setProperty(\n    \"--card-height-hover\",\n    `${new_card_height_hover}px`\n  );\n\n  return true;\n};\n"], "names": ["SELECTORS", "renderCourses", "tab", "args", "filters", "$loading", "$currentTabCards", "$otherTabsCards", "addClass", "request", "methodname", "mycourses", "allcourses", "Ajax", "call", "getCourses", "then", "async", "paginationData", "preparePagePagination", "data", "cards", "Templates", "render", "pagination", "html", "removeClass", "resetCardsHeight", "tooltip", "initCollapseInModal", "init", "catch", "error", "console", "Notification", "exception", "getCurrentTab", "renderFilters", "document", "on", "this", "replace", "e", "preventDefault", "val", "filter", "prop", "getFiltersData", "log", "getCurrentPage", "page", "$button", "courseid", "status", "result", "API", "set_favourite", "courses", "component", "id", "favourite", "warnings", "for<PERSON>ach", "warning", "addNotification", "message", "type", "toggle", "find", "toggleClass", "window", "showall", "closeall", "querySelectorAll", "button", "addEventListener", "targetId", "getAttribute", "collapses", "getElementById", "collapse", "removeAttribute", "allOpen", "Array", "from", "every", "el", "classList", "contains", "collapseId", "trigger", "querySelector", "add", "setAttribute", "remove", "textContent", "filtered", "getTabFilters", "results", "total_pages", "isMobile<PERSON>iew", "innerWidth", "label", "pages", "haspages", "pagesize", "per_page", "currentPage", "totalPages", "previous", "url", "next", "treshold", "i", "push", "active", "last", "first", "formData", "name", "checked", "transformData", "value", "cardSelector", "container", "length", "cardWidths", "map", "card", "scrollWidth", "new_image_height", "Math", "max", "new_card_height", "style", "setProperty", "cardBodySelector", "cardBodies", "cardBodyHeights", "body", "scrollHeight", "new_card_height_hover"], "mappings": "ilDAWMA,yBACY,gBADZA,kBAEK,yBAFLA,uBAGU,8DAHVA,qBAIQ,mDAmKCC,cAAcC,SAAKC,4DAAO,CAAEC,QAAS,IAC9CC,UAAW,mBAAEL,yBAA2B,mBACxCM,kBAAmB,mBACrBN,yBAA2B,cAAgBE,IAAM,WAE/CK,iBAAkB,mBACpBP,yBAA2B,mBAAqBE,IAAM,YAGxDG,SAASG,SAAS,+BAhDZC,QAAU,CACdC,WANW,CACXC,UAAW,oCACXC,WAAY,6FAHgB,aAQ5BT,4DARgD,WAW3CU,cAAKC,KAAK,CAACL,UAAU,GA6C5BM,CAAWb,IAAKC,MACba,MAAKC,iBACAC,eAAiBC,sBAAsBC,MAEvCC,YAAcC,mBAAUC,OAC1B,iCACAH,MAEEI,WAAaN,qBACPI,mBAAUC,OACd,gCACAL,gBAEF,GAEJZ,iBAAiBmB,KAAKJ,2BACpBrB,sBAAsByB,KAAKD,YAE7BnB,SAASqB,YAAY,QACrBnB,gBAAgBkB,KAAK,IAErBE,uCAEE,kBAAkBC,UAEpBC,yCAEUC,KAAK,yBAEhBC,OAAOC,QACNC,QAAQD,MAAMA,OACdE,aAAaC,6BApMClB,qBACdf,IAAMkC,gBAEVnC,cAAcC,KACdmC,cAAcnC,yBAEZoC,UACCC,GAAG,QAASvC,yBAA2B,aAAa,eAC/CE,KAAM,mBAAEsC,MAAMpB,KAAK,UAAUqB,QAAQ,IAAK,wBAC5CzC,sBAAsByB,KAAK,IAE7BxB,cAAcC,KACdmC,cAAcnC,QAEfqC,GACC,QACAvC,yBAA2B,uBAC3B,SAAU0C,GACRA,EAAEC,qBAEEzC,IAAMkC,oCAERpC,wBAAwB4C,IAAI,wBAC5B5C,wBAAwB6C,OAAO,YAAYC,KAAK,WAAW,uBAC3D9C,wBAAwB6C,OAAO,gBAAgBC,KAAK,WAAW,uBAC/D9C,wBAAwB6C,OAAO,iBAAiBD,IAAI,0BACpD5C,wBAAwB6C,OAAO,0BAA0BD,IAAI,MAE/D3C,cAAcC,QAGjBqC,GAAG,SAAUvC,yBAA2B,kBAAmB0C,IAC1DA,EAAEC,qBAEEzC,IAAMkC,gBACNhC,QAAU2C,iBAEdd,QAAQe,IAAI,UAAW5C,SAEvBH,cAAcC,IAAK,CAAEE,QAASA,SAA9B,IAEDmC,GAAG,QAASvC,qBAAuB,eAAgB0C,IAClDA,EAAEC,qBAEEzC,IAAMkC,gBACNhC,QAAU2C,iBACIE,iBAIlBhD,cAAcC,IAAK,CAAEgD,MAHL,mBAAEV,MAAMpB,KAAK,QAGIhB,QAASA,SAA1C,IAEDmC,GAAG,QAAS,0BAA0BtB,UACrCyB,EAAEC,qBAEEQ,SAAU,mBAAEX,MACZY,SAAWD,QAAQ/B,KAAK,YACxBiC,OAC2B,GAA7BF,QAAQ/B,KAAK,cAAkD,GAA7B+B,QAAQ/B,KAAK,uBAGzCkC,aAAeC,aAAIC,cAAc,CACrCC,QAAS,CACP,CACEC,UAAW,KACXC,GAAIP,SACJQ,UAAWP,WAKbC,OAAOO,UACTP,OAAOO,SAASC,SAASC,UACvB7B,aAAa8B,gBAAgB,CAC3BC,QAASF,QAAQE,QACjBC,KAAM,iBAKRhE,IAAMkC,gBACNhC,QAAU2C,iBACVG,KAAOD,iBACPkB,OAASd,OAAS,EAAI,EAE1BF,QAAQ/B,KAAK,YAAa+C,QAC1BhB,QAAQiB,KAAK,KAAKC,YAAY,cAAeF,QAC7ChB,QAAQiB,KAAK,KAAKC,YAAY,KAAMF,QAEpClE,cAAcC,IAAK,CAAEgD,KAAMA,KAAM9C,QAASA,UAC1C,MAAO4B,OACPE,aAAa8B,gBAAgB,CAC3BC,QAAS,8BACTC,KAAM,UAERjC,QAAQD,MAAMA,+BAIlBsC,QAAQ/B,GAAG,SAAUZ,yBA2GnBE,oBAAsBZ,gBACpBsD,cAAgB,mBAAU,aAC1BC,eAAiB,mBAAU,eAEjClC,SAASmC,iBAAiB,4BAA4BX,SAASY,SAC7DA,OAAOC,iBAAiB,SAAUjC,IAChCA,EAAEC,uBAEIiC,SAAWF,OAAOG,aAAa,0BAE/BC,UADSxC,SAASyC,eAAeH,UACdH,iBAAiB,aAG1CK,UAAUhB,SAASkB,WACjBA,SAASC,gBAAgB,wBAIrBC,QAAUC,MAAMC,KAAKN,WAAWO,OAAOC,IAC3CA,GAAGC,UAAUC,SAAS,UAGxBV,UAAUhB,SAASkB,iBACXS,WAAaT,SAASH,aAAa,MACnCa,QAAUpD,SAASqD,gCAAyBF,kBAE9CP,6BAEAF,UAAUA,SAAS,QACjBU,UACFA,QAAQH,UAAUK,IAAI,aACtBF,QAAQG,aAAa,gBAAiB,gCAItCb,UAAUA,SAAS,QACjBU,UACFA,QAAQH,UAAUO,OAAO,aACzBJ,QAAQG,aAAa,gBAAiB,aAM5CnB,OAAOqB,YAAcb,QAAUX,QAAUC,QAAzC,uBAYSnC,cAAcnC,SAAK8F,gEAAW,aAEnC5E,kCArIFX,QAAU,CACdC,WAAY,iCACZP,KAAM,CACJD,2DAJ6B,YAK7B8F,gEALqD,YASlDnF,cAAKC,KAAK,CAACL,UAAU,GA6HPwF,CAAc/F,IAAK8F,UAChC5F,cAAgBkB,mBAAUC,OAAO,mCAAoC,CACzEnB,QAASgB,2BAETpB,mBAAmByB,KAAKrB,SAC1B,MAAO4B,OACPC,QAAQD,MAAM,8BAA+BA,cAS3CiB,eAAiB,KAEnB,mBAAEjD,qBAAuB,uCAAuCoB,KAC9D,SACG,EAqBHD,sBAAyB+E,aACxBA,gBAAAA,QAASC,aAAsC,GAAvBD,QAAQC,kBAC5B,OAGLC,aAAe9B,OAAO+B,YAAc,IACpC7E,WAAa,CACf8E,MAAO,OACPC,MAAO,GACPC,UAAU,EACVC,SAAUP,QAAQQ,UAAY,IAG5BC,YAAcT,QAAQhD,KACtB0D,WAAaV,QAAQC,YAErBQ,YAAc,IAChBnF,WAAWqF,SAAW,CACpB3D,KAAMyD,YAAc,EACpBG,oBAAcH,YAAc,KAI5BA,YAAcC,aAChBpF,WAAWuF,KAAO,CAChB7D,KAAMyD,YAAc,EACpBG,oBAAcH,YAAc,SAI5BK,SAAW,KACXJ,YAAc,MACX,IAAIK,EAAI,EAAGA,GAAKL,WAAYK,IAC/BzF,WAAW+E,MAAMW,KAAK,CACpBhE,KAAM+D,EACNE,OAAQF,IAAMN,YACdG,oBAAcG,aAKfN,aAAe,IAAMP,cACrBO,aAAe,GAAKP,aACrB,IAEIO,aAAeC,WAAa,IAC9BI,SAAW,GAGRZ,iBAaE,IAAIa,EAAI,EAAGA,GAAK,EAAGA,IACtBzF,WAAW+E,MAAMW,KAAK,CACpBhE,KAAM+D,EACNE,OAAQF,IAAMN,YACdG,oBAAcG,cAfhB,IAAIA,EAAI,EACRA,GAAKN,YAAc,GAAKM,GAAKL,WAAaI,SAC1CC,IAEAzF,WAAW+E,MAAMW,KAAK,CACpBhE,KAAM+D,EACNE,OAAQF,IAAMN,YACdG,oBAAcG,KAaJ,GAAZD,UAAiBZ,aACnB5E,WAAW4F,KAAO,CAChBlE,KAAM0D,WACNO,OAAQP,YAAcD,YACtBG,oBAAcF,aAEPD,YAAc,EAAIC,YAC3BpF,WAAW+E,MAAMW,KAAK,CACpBhE,KAAM0D,WACNO,OAAQP,aAAeD,YACvBG,oBAAcF,mBAGb,GACJD,YAAcC,WAAa,IAAMR,cACjCO,YAAcC,WAAa,GAAKR,aACjC,CAEAY,SAAW,EACPZ,aACEO,YAAc,IAChBK,SAAWJ,WAAa,EACxBpF,WAAW6F,MAAQ,CACjBnE,KAAM,EACNiE,OAAQ,GAAKR,YACbG,oBAAc,KAGTH,aAAe,IACxBK,SAAWL,YAAc,EACzBnF,WAAW6F,MAAQ,CACjBnE,KAAM,EACNiE,OAAQ,GAAKR,YACbG,oBAAc,SAIb,IAAIG,EAAID,SAAUC,GAAKL,WAAYK,IACtCzF,WAAW+E,MAAMW,KAAK,CACpBhE,KAAM+D,EACNE,OAAQF,IAAMN,YACdG,oBAAcG,SAGb,IAELzF,WAAW6F,MAAQ,CACjBnE,KAAM,EACNiE,OAAQ,GAAKR,YACbG,oBAAc,IAEZV,aACF5E,WAAW+E,MAAMW,KAAK,CACpBhE,KAAMyD,YACNQ,QAAQ,EACRL,oBAAcH,wBAGX,IAAIM,EAAIN,YAAc,EAAGM,GAAKN,YAAc,EAAGM,IAClDzF,WAAW+E,MAAMW,KAAK,CACpBhE,KAAM+D,EACNE,OAAQF,IAAMN,YACdG,oBAAcG,KAIpBzF,WAAW4F,KAAO,CAChBlE,KAAM0D,WACNO,OAAQP,YAAcD,YACtBG,oBAAcF,oBAIbpF,UAAP,EAQIuB,eAAiB,WACfuE,SAAW,UAEJhF,SAASqD,cACpB3F,yBAA2B,kBAExByE,iBAAiB,2BAA2BX,SAASwB,KACpDA,GAAGiC,OACW,aAAZjC,GAAGpB,KACLoD,SAAShC,GAAGiC,MAAQjC,GAAGkC,QACF,UAAZlC,GAAGpB,KACRoB,GAAGkC,UACLF,SAAShC,GAAGiC,MAAQE,cAAcnC,KAGpCgC,SAAShC,GAAGiC,MAAQE,cAAcnC,QAKjCgC,QAAP,EAYIG,cAAiBrG,cACbA,KAAKmG,UACN,mBACmB,KAAfnG,KAAKsG,MAAetG,KAAKsG,MAAQ,MACrC,aAEA,wBACmB,KAAftG,KAAKsG,MAAetG,KAAKsG,OAAS,iBAElCtG,KAAKsG,QAaZtF,cAAgB,KACH,mBAAEpC,yBAA2B,oBAE5BoB,KAAK,UAAUqB,QAAQ,IAAK,IAW1Cd,iBAAmB,WAGjBgG,aAAe3H,yBAA2B,SAC1C4H,UAAYtF,SAASqD,cAAc3F,0BAEnCqB,MAAQiB,SAASmC,iBAAiBkD,kBACnCtG,MAAMwG,OAAQ,OAAO,QAEpBC,WAAa3C,MAAMC,KAAK/D,OAAO0G,KAAKC,MAASA,KAAKC,cAGlDC,iBAVoB,IAQPC,KAAKC,OAAON,YATN,IAYnBO,gBAAkBH,iBAAmB,GAE3CN,UAAUU,MAAMC,YAAY,yBAAmBL,wBAC/CN,UAAUU,MAAMC,YAAY,0BAAoBF,6BAE1CG,iBAAmBb,aAAe,cAClCc,WAAanG,SAASmC,iBAAiB+D,kBAEvCE,gBAAkBvD,MAAMC,KAAKqD,YAAYV,KAC5CY,MAASA,KAAKC,eAIXC,sBAFmBV,KAAKC,OAAOM,iBAEYR,iBAAmB,SACpEN,UAAUU,MAAMC,YACd,gCACGM,8BAGE,CAAP"}