/* eslint-disable complexity */
/* eslint-disable no-console */
import $ from "jquery";
import Ajax from "core/ajax";
import Templates from "core/templates";
import * as Notification from "core/notification";
import API from "local_courseblockapi/api";
import "theme_boost/bootstrap/tooltip";
import { get_string as getString } from "core/str";
import Favourite from "local_courseblockapi/favourite";

const SELECTORS = {
  blockContainer: ".mycoursetabs",
  filters: ".mycoursetabs .filters",
  filtersInput: ".mycoursetabs .filters input, .mycoursetabs .filters select",
  pagination: ".mycoursetabs .pagination-wrapper",
  footerContainer: ".mycoursetabs .courses-page-footer",
};

/**
 * Initializes the My Course Tabs block.
 *
 * @return {Promise.<void>} A promise that resolves when the initialization is complete.
 */
export const init = async function () {
  let tab = getCurrentTab();

  renderCourses(tab);
  renderFilters(tab);

  $(document)
    .on("click", SELECTORS.blockContainer + " .tablink", function () {
      let tab = $(this).data("target").replace("#", "");
      $(SELECTORS.pagination).html("");

      renderCourses(tab);
      renderFilters(tab);
    })
    .on(
      "click",
      SELECTORS.blockContainer + " .btn-clear-filters",
      function (e) {
        e.preventDefault();

        let tab = getCurrentTab();

        $(SELECTORS.filtersInput).val("");
        $(SELECTORS.filtersInput).filter(":checked").prop("checked", false);
        $(SELECTORS.filtersInput).filter("#recommended").prop("checked", true);
        $(SELECTORS.filtersInput).filter("#input-status").val("-1");
        $(SELECTORS.filtersInput).filter("#input-status-progress").val("-1");

        renderCourses(tab);
      }
    )
    .on("change", SELECTORS.blockContainer + " .filters form", (e) => {
      e.preventDefault();

      let tab = getCurrentTab();
      let filters = getFiltersData();

      console.log("filters", filters);

      renderCourses(tab, { filters: filters });
    })
    .on("click", SELECTORS.pagination + " .page-link", (e) => {
      e.preventDefault();

      let tab = getCurrentTab();
      let filters = getFiltersData();
      let currentPage = getCurrentPage();
      let pageValue = $(this).data("page");
      let page = pageValue;

      renderCourses(tab, { page: page, filters: filters });
    })
    .on("click", ".card-course-favourite", async (e) => {
      e.preventDefault();

      let $button = $(this);
      let courseid = $button.data("courseid");
      let status =
        $button.data("favourite") == 0 || $button.data("favourite") == false;

      try {
        const result = await API.set_favourite({
          courses: [
            {
              component: null,
              id: courseid,
              favourite: status,
            },
          ],
        });

        if (result.warnings) {
          result.warnings.forEach((warning) => {
            Notification.addNotification({
              message: warning.message,
              type: "error",
            });
          });
        }

        let tab = getCurrentTab();
        let filters = getFiltersData();
        let page = getCurrentPage();
        let toggle = status ? 1 : 0;

        $button.data("favourite", toggle);
        $button.find("i").toggleClass("fa-regular", !toggle);
        $button.find("i").toggleClass("fa", toggle);

        renderCourses(tab, { page: page, filters: filters });
      } catch (error) {
        Notification.addNotification({
          message: "Erro ao atualizar favorito.",
          type: "error",
        });
        console.error(error);
      }
    });

  $(window).on("resize", resetCardsHeight);
};

/**
 * Get courses for a tab
 *
 * @param {string} [tab=mycourses] The tab to get the courses for
 * @param {Object} [args={}] Additional arguments to pass to the webservice
 * @return {Promise.<Object[]>} A promise that resolves with the courses for the given tab
 */
async function getCourses(tab = "mycourses", args = {}) {
  let method = {
    mycourses: "block_mycoursetabs_get_my_courses",
    allcourses: "block_mycoursetabs_get_all_courses",
  };

  const request = {
    methodname: method[tab],
    args: args,
  };

  return Ajax.call([request])[0];
}

/**
 * Fetches filters for a specified tab.
 *
 * @param {string} [tab="mycourses"] - The tab for which filters are to be retrieved.
 * @param {Object} [filtered={}] - Additional filter criteria.
 * @return {Promise.<Object>} A promise that resolves with the filters for the specified tab.
 */
async function getTabFilters(tab = "mycourses", filtered = {}) {
  const request = {
    methodname: "block_mycoursetabs_get_filters",
    args: {
      tab: tab,
      filtered: filtered,
    },
  };

  return Ajax.call([request])[0];
}

/**
 * Renders the courses for a specified tab.
 *
 * Fetches courses using the provided tab and arguments, displays a loading indicator during the
 * fetch, and updates the UI with the retrieved course cards and pagination. Clears the course
 * cards of other tabs and adjusts card height upon completion.
 *
 * @param {string} tab - The tab for which courses are to be rendered.
 * @param {Object} [args={ filters: {} }] - Additional arguments to pass for fetching courses,
 *                                          including filters.
 * @return {Promise.<void>} A promise that resolves when the courses have been rendered.
 */
async function renderCourses(tab, args = { filters: {} }) {
  let $loading = $(SELECTORS.blockContainer + " .smart-loading");
  let $currentTabCards = $(
    SELECTORS.blockContainer + " .tab-pane#" + tab + " .cards"
  );
  let $otherTabsCards = $(
    SELECTORS.blockContainer + " .tab-pane:not(#" + tab + ") .cards"
  );

  $loading.addClass("show");

  getCourses(tab, args)
    .then(async (data) => {
      let paginationData = preparePagePagination(data);

      let cards = await Templates.render(
        "block_mycoursetabs/block-cards",
        data
      );
      let pagination = paginationData
        ? await Templates.render(
            "block_mycoursetabs/pagination",
            paginationData
          )
        : "";

      $currentTabCards.html(cards);
      $(SELECTORS.pagination).html(pagination);

      $loading.removeClass("show");
      $otherTabsCards.html("");

      resetCardsHeight();

      $("[data-tooltip]").tooltip();

      initCollapseInModal();

      Favourite.init("block_mycoursetabs");
    })
    .catch((error) => {
      console.error(error);
      Notification.exception();
    });
}

/**
 * Initializes click event listeners for elements with the 'data-collapse-sections' attribute.
 *
 * When an element is clicked, toggles the collapse state of associated sections.
 * If all sections are currently expanded, they will be collapsed, and vice versa.
 */
const initCollapseInModal = async () => {
  const showall = await getString("expandall");
  const closeall = await getString("collapseall");

  document.querySelectorAll("[data-collapse-sections]").forEach((button) => {
    button.addEventListener("click", (e) => {
      e.preventDefault();

      const targetId = button.getAttribute("data-collapse-sections");
      const target = document.getElementById(targetId);
      const collapses = target.querySelectorAll(".collapse");

      // Remove o atributo data-parent (caso exista)
      collapses.forEach((collapse) => {
        collapse.removeAttribute("data-parent");
      });

      // Verifica se todos estão abertos (classe "show" = aberto)
      const allOpen = Array.from(collapses).every((el) =>
        el.classList.contains("show")
      );

      collapses.forEach((collapse) => {
        const collapseId = collapse.getAttribute("id");
        const trigger = document.querySelector(`[href="#${collapseId}"]`);

        if (allOpen) {
          // Ocultar o collapse simulando clique no botão
          $(collapse).collapse("hide");
          if (trigger) {
            trigger.classList.add("collapsed");
            trigger.setAttribute("aria-expanded", "false");
          }
        } else {
          // Mostrar o collapse
          $(collapse).collapse("show");
          if (trigger) {
            trigger.classList.remove("collapsed");
            trigger.setAttribute("aria-expanded", "true");
          }
        }
      });

      // Atualizar o texto do botão principal
      button.textContent = allOpen ? showall : closeall;
    });
  });
};

/**
 * Renders the filters for a specified tab.
 *
 * @param {string} [tab="mycourses"] - The tab for which filters are to be rendered.
 * @param {Object} [filtered={}] - Additional filter criteria.
 * @return {Promise.<void>} A promise that resolves when the filters have been rendered.
 */
async function renderFilters(tab, filtered = {}) {
  try {
    const data = await getTabFilters(tab, filtered);
    const filters = await Templates.render("block_mycoursetabs/block-filters", {
      filters: data,
    });
    $(SELECTORS.filters).html(filters);
  } catch (error) {
    console.error("Erro ao renderizar filtros:", error);
  }
}

/**
 * Gets the current page number from the pagination buttons.
 *
 * @return {number} The current page number.
 */
const getCurrentPage = () => {
  return (
    $(SELECTORS.pagination + " .page-item.active button.page-link").data(
      "page"
    ) || 1
  );
};

/**
 * Prepare the pagination object from a given results object.
 *
 * @param {object} results The results object with the following properties:
 *   - total_pages: The total number of pages.
 *   - page: The current page number.
 *   - per_page: The number of items per page.
 *
 * @return {object} The pagination object with the following properties:
 *   - label: The label to display in the pagination.
 *   - pages: An array of page objects with the following properties:
 *     - page: The page number.
 *     - active: Whether the page is currently active.
 *     - url: The URL to go to when the page is clicked.
 *   - haspages: Whether there are multiple pages.
 *   - pagesize: The number of items per page.
 */
const preparePagePagination = (results) => {
  if (!results?.total_pages || results.total_pages == 1) {
    return {};
  }

  let isMobileView = window.innerWidth <= 600; /*  */
  let pagination = {
    label: "Page",
    pages: [],
    haspages: true,
    pagesize: results.per_page || 10,
  };

  let currentPage = results.page;
  let totalPages = results.total_pages;

  if (currentPage > 1) {
    pagination.previous = {
      page: currentPage - 1,
      url: `?page=${currentPage - 1}`,
    };
  }

  if (currentPage < totalPages) {
    pagination.next = {
      page: currentPage + 1,
      url: `?page=${currentPage + 1}`,
    };
  }

  let treshold = 0;
  if (totalPages <= 5) {
    for (let i = 1; i <= totalPages; i++) {
      pagination.pages.push({
        page: i,
        active: i === currentPage,
        url: `?page=${i}`,
      });
    }
  } else {
    if (
      (currentPage <= 5 && !isMobileView) ||
      (currentPage <= 3 && isMobileView)
    ) {
      // Primeiras páginas
      if (currentPage <= totalPages - 5) {
        treshold = 2;
      }

      if (!isMobileView) {
        for (
          let i = 1;
          i <= currentPage + 3 && i <= totalPages - treshold;
          i++
        ) {
          pagination.pages.push({
            page: i,
            active: i === currentPage,
            url: `?page=${i}`,
          });
        }
      } else {
        for (let i = 1; i <= 3; i++) {
          pagination.pages.push({
            page: i,
            active: i === currentPage,
            url: `?page=${i}`,
          });
        }
      }

      if (treshold == 2 || isMobileView) {
        pagination.last = {
          page: totalPages,
          active: totalPages == currentPage,
          url: `?page=${totalPages}`,
        };
      } else if (currentPage + 3 < totalPages) {
        pagination.pages.push({
          page: totalPages,
          active: totalPages === currentPage,
          url: `?page=${totalPages}`,
        });
      }
    } else if (
      (currentPage > totalPages - 5 && !isMobileView) ||
      (currentPage > totalPages - 3 && isMobileView)
    ) {
      // últimas páginas
      treshold = 1;
      if (isMobileView) {
        if (currentPage > 3) {
          treshold = totalPages - 2;
          pagination.first = {
            page: 1,
            active: 1 == currentPage,
            url: `?page=${1}`,
          };
        }
      } else if (currentPage >= 6) {
        treshold = currentPage - 3;
        pagination.first = {
          page: 1,
          active: 1 == currentPage,
          url: `?page=${1}`,
        };
      }

      for (let i = treshold; i <= totalPages; i++) {
        pagination.pages.push({
          page: i,
          active: i === currentPage,
          url: `?page=${i}`,
        });
      }
    } else {
      // Meio
      pagination.first = {
        page: 1,
        active: 1 == currentPage,
        url: `?page=${1}`,
      };
      if (isMobileView) {
        pagination.pages.push({
          page: currentPage,
          active: true,
          url: `?page=${currentPage}`,
        });
      } else {
        for (let i = currentPage - 3; i <= currentPage + 3; i++) {
          pagination.pages.push({
            page: i,
            active: i === currentPage,
            url: `?page=${i}`,
          });
        }
      }
      pagination.last = {
        page: totalPages,
        active: totalPages == currentPage,
        url: `?page=${totalPages}`,
      };
    }
  }
  return pagination;
};

/**
 * Get the filters data from the current page.
 *
 * @return {Object} filters
 */
const getFiltersData = () => {
  const formData = {};

  const form = document.querySelector(
    SELECTORS.blockContainer + " .filters form"
  );
  form.querySelectorAll("input, textarea, select").forEach((el) => {
    if (el.name) {
      if (el.type === "checkbox") {
        formData[el.name] = el.checked;
      } else if (el.type === "radio") {
        if (el.checked) {
          formData[el.name] = transformData(el);
        }
      } else {
        formData[el.name] = transformData(el);
      }
    }
  });

  return formData;
};

/**
 * Transforms the input data based on its name.
 *
 * @param {Object} data - The data object containing name and value properties.
 * @param {string} data.name - The name of the data field.
 * @param {string} data.value - The value of the data field.
 * @returns {string|number} - Returns the value as is for default case,
 *   or 0 if value is an empty string for "status" or "categoryid".
 */
const transformData = (data) => {
  switch (data.name) {
    case "categoryid":
      return data.value !== "" ? data.value : 0;
    case "status":
      return data.value !== "" ? data.value : -1;
    case "status_progress":
      return data.value !== "" ? data.value : -1;
    default:
      return data.value;
  }
};

/**
 * Retrieves the identifier of the currently active tab.
 *
 * Queries the DOM for the active tab link within the block container
 * and extracts the data-target attribute, returning it without the
 * leading "#" character.
 *
 * @return {string} The identifier of the active tab.
 */
const getCurrentTab = () => {
  let $activeTab = $(SELECTORS.blockContainer + " .tablink.active");

  return $activeTab.data("target").replace("#", "");
};

/**
 * Resets the height of cards within the block container based on their widths and original image dimensions.
 *
 * This function calculates the new height for card images and card bodies, updating CSS variables
 * to reflect these new dimensions. The calculated heights include additional pixels for styling purposes.
 *
 * @return {boolean} Returns false if no cards are found, otherwise true.
 */
const resetCardsHeight = () => {
  const imgOriginalWidth = 600;
  const imgOriginalHeight = 350;
  const cardSelector = SELECTORS.blockContainer + " .card";
  const container = document.querySelector(SELECTORS.blockContainer);

  const cards = document.querySelectorAll(cardSelector);
  if (!cards.length) return false;

  const cardWidths = Array.from(cards).map((card) => card.scrollWidth);
  const card_width = Math.max(...cardWidths);

  const new_image_height = (card_width * imgOriginalHeight) / imgOriginalWidth;
  const new_card_height = new_image_height + 16; // add 16px

  container.style.setProperty("--img-height", `${new_image_height}px`);
  container.style.setProperty("--card-height", `${new_card_height}px`);

  const cardBodySelector = cardSelector + " .card-body";
  const cardBodies = document.querySelectorAll(cardBodySelector);

  const cardBodyHeights = Array.from(cardBodies).map(
    (body) => body.scrollHeight
  );
  const card_body_height = Math.max(...cardBodyHeights);

  const new_card_height_hover = card_body_height + new_image_height + 5;
  container.style.setProperty(
    "--card-height-hover",
    `${new_card_height_hover}px`
  );

  return true;
};
