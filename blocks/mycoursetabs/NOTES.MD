All

SELECT 
	mc.id,
    mlam.userid,
    mc.fullname,
    mcc.id AS categoryid,
    mcc.name AS category,
	mf.id IS NOT NULL AS is_favorite,
    0 AS showprogress,
    COALESCE(SUM(mcdv.view_counter), 0) AS access_count,
    mlocl.id AS offerclass, 
    mloc.id AS offercourse
FROM mdl_local_audience_members mlam
	JOIN mdl_local_offermanager_audience mloa ON mloa.audienceid = mlam.audienceid 
	JOIN mdl_local_offermanager mlo ON mlo.id = mloa.offerid
	JOIN mdl_local_offermanager_course mloc ON mloc.offerid = mlo.id
	JOIN mdl_local_offermanager_class mlocl ON mlocl.offercourseid = mloc.id
	JOIN mdl_course mc ON mc.id = mloc.courseid AND mc.visible = 1
	JOIN mdl_course_categories mcc ON mcc.id = mc.category AND mcc.visible = 1
	LEFT JOIN mdl_local_custom_fields_course mlcfc ON mlcfc.courseid = mc.id
    LEFT JOIN mdl_favourite mf ON mf.itemid = mc.id AND mf.userid = mlam.userid AND mf.itemtype = 'courses'
    LEFT JOIN mdl_courseviews_daily_views mcdv 
        ON mcdv.courseid = mc.id 
        AND mcdv.viewing_date >= UNIX_TIMESTAMP(CURDATE() - INTERVAL 30 DAY)
WHERE 
    mlam.userid = 28 
    AND NOT EXISTS (
        SELECT 1 
        FROM mdl_local_offermanager_ue ue
        WHERE ue.courseid = mc.id
          AND ue.userid = mlam.userid
    )
	AND mlo.status = 1 
    AND mloc.status = 1 
    AND mlocl.status = 1
GROUP BY mc.id
ORDER BY access_count DESC;
